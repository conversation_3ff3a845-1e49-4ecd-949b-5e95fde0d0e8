using S2M.DomainModelLayer.Enums;
using System;

namespace S2M.ApplicationLayer.Dto
{
    /// <summary>
    /// DTO per i ruoli delle unità organizzative nei flussi
    /// </summary>
    public class FlowOrganizationUnitRoleDto
    {
        /// <summary>
        /// ID del ruolo
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// ID del flusso
        /// </summary>
        public Guid FlowId { get; set; }

        /// <summary>
        /// ID dell'unità organizzativa
        /// </summary>
        public Guid OrganizationUnitId { get; set; }

        /// <summary>
        /// Nome dell'unità organizzativa
        /// </summary>
        public string OrganizationUnitName { get; set; }

        /// <summary>
        /// Email dell'unità organizzativa
        /// </summary>
        public string OrganizationUnitEmail { get; set; }

        /// <summary>
        /// Ruolo dell'unità organizzativa
        /// </summary>
        public FlowOrganizationUnitRoleType Role { get; set; }

        /// <summary>
        /// Stato di riconoscimento
        /// </summary>
        public bool? Acknowledge { get; set; }

        /// <summary>
        /// Data di riconoscimento
        /// </summary>
        public DateTime? AcknowledgeDate { get; set; }
    }
} 