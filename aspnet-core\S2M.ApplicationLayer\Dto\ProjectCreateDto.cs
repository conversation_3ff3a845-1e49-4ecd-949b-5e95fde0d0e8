using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace S2M.ApplicationLayer.Dto
{
    /// <summary>
    /// DTO per la creazione di un Project
    /// </summary>
    public class ProjectCreateDto
    {
        /// <summary>
        /// ID dell'area
        /// </summary>
        [Required(ErrorMessage = "L'ID dell'area è obbligatorio")]
        public Guid AreaId { get; set; }

        /// <summary>
        /// Nome del progetto
        /// </summary>
        [Required(ErrorMessage = "Il nome del progetto è obbligatorio")]
        [MaxLength(256, ErrorMessage = "Il nome del progetto non può superare i 256 caratteri")]
        public string Name { get; set; }

        /// <summary>
        /// Sottotitolo del progetto
        /// </summary>
        [MaxLength(1024, ErrorMessage = "Il sottotitolo del progetto non può superare i 1024 caratteri")]
        public string Subtitle { get; set; }

        /// <summary>
        /// Stato finale
        /// </summary>
        [MaxLength(256, ErrorMessage = "Lo stato finale non può superare i 256 caratteri")]
        public string EndState { get; set; }

        /// <summary>
        /// Riepilogo
        /// </summary>
        [MaxLength(2048, ErrorMessage = "Il riepilogo non può superare i 2048 caratteri")]
        public string Summary { get; set; }

        /// <summary>
        /// ID del referente
        /// </summary>
        [Required(ErrorMessage = "Il referente è obbligatorio")]
        public Guid OwnerId { get; set; }

        /// <summary>
        /// ID dei collaboratori
        /// </summary>
        public List<Guid>? CollaboratorIds { get; set; }
    }
} 