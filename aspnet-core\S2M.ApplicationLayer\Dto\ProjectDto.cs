using System;
using System.Collections.Generic;

namespace S2M.ApplicationLayer.Dto
{
    /// <summary>
    /// DTO per l'entità Project
    /// </summary>
    public class ProjectDto : BaseDto
    {
        /// <summary>
        /// ID dell'area
        /// </summary>
        public Guid AreaId { get; set; }

        /// <summary>
        /// Nome del progetto
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Sottotitolo del progetto
        /// </summary>
        public string Subtitle { get; set; }

        /// <summary>
        /// Stato finale
        /// </summary>
        public string EndState { get; set; }

        /// <summary>
        /// Riepilogo
        /// </summary>
        public string Summary { get; set; }

        /// <summary>
        /// Area associata
        /// </summary>
        public AreaDto Area { get; set; }

        /// <summary>
        /// Milestone del progetto
        /// </summary>
        public List<ProjectMilestoneDto> Milestones { get; set; }

        /// <summary>
        /// Referente del progetto
        /// </summary>
        public OrganizationUnitDto Owner { get; set; }

        /// <summary>
        /// Collaboratori del progetto
        /// </summary>
        public List<OrganizationUnitDto> Collaborators { get; set; }
    }
} 