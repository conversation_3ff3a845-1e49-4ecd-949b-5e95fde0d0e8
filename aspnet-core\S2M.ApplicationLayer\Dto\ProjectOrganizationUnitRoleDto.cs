using S2M.DomainModelLayer.Enums;
using System;

namespace S2M.ApplicationLayer.Dto
{
    /// <summary>
    /// DTO per l'entità ProjectOrganizationUnitRole
    /// </summary>
    public class ProjectOrganizationUnitRoleDto : BaseDto
    {
        /// <summary>
        /// ID del progetto
        /// </summary>
        public Guid ProjectId { get; set; }

        /// <summary>
        /// ID dell'unità organizzativa
        /// </summary>
        public Guid OrganizationUnitId { get; set; }

        /// <summary>
        /// Tipo di ruolo dell'unità organizzativa nel progetto
        /// </summary>
        public ProjectOrganizationUnitRoleType UnitRole { get; set; }

        /// <summary>
        /// Nome del progetto
        /// </summary>
        public string ProjectName { get; set; }

        /// <summary>
        /// Nome dell'unità organizzativa
        /// </summary>
        public string OrganizationUnitName { get; set; }

        /// <summary>
        /// Email dell'unità organizzativa
        /// </summary>
        public string OrganizationUnitEmail { get; set; }
    }


} 