using AutoMapper;
using Microsoft.Extensions.DependencyInjection;
using S2M.ApplicationLayer.Dto;
using S2M.DomainModelLayer.Entities;
using System;
using System.Linq;

namespace S2M.ApplicationLayer.Extensions
{
    /// <summary>
    /// Estensioni per la configurazione del mapping
    /// </summary>
    public static class MappingExtensions
    {
        /// <summary>
        /// Aggiunge i servizi di mapping al container di dependency injection
        /// </summary>
        /// <param name="services">Collection di servizi</param>
        /// <returns>Collection di servizi aggiornata</returns>
        public static IServiceCollection AddAutoMapperProfiles(this IServiceCollection services)
        {
            services.AddAutoMapper(config =>
            {
                // Configurazione dei profili di mapping
                config.CreateMap<Flow, FlowDto>()
                    .ForMember(dest => dest.Attributes, opt => opt.MapFrom(src => src.Attributes))
                    .ForMember(dest => dest.OrganizationUnitName, opt => opt.MapFrom(src => src.OrganizationUnit != null ? src.OrganizationUnit.Name : null))
                    .ForMember(dest => dest.CreatorFirstName, opt => opt.Ignore())
                    .ForMember(dest => dest.CreatorLastName, opt => opt.Ignore())
                    .ForMember(dest => dest.CreatorRank, opt => opt.Ignore())
                    .ReverseMap()
                    .ForMember(dest => dest.Attributes, opt => opt.Ignore())
                    .ForMember(dest => dest.OrganizationUnit, opt => opt.Ignore());
                config.CreateMap<Flow, FlowCreateDto>()
                    .ForMember(dest => dest.Attributes, opt => opt.Ignore())
                    .ReverseMap()
                    .ForMember(dest => dest.Attributes, opt => opt.MapFrom(src => src.Attributes));
                config.CreateMap<FlowType, FlowTypeDto>().ReverseMap();

                // Mapping per gli attributi
                config.CreateMap<DomainModelLayer.Entities.Attribute, AttributeDto>().ReverseMap();

                // Add mapping for FlowAttributeCreateDto to Attribute
                config.CreateMap<FlowAttributeCreateDto, DomainModelLayer.Entities.Attribute>()
                    .ForMember(dest => dest.Id, opt => opt.MapFrom(src => Guid.NewGuid()))
                    .ForMember(dest => dest.CreationTime, opt => opt.MapFrom(src => DateTime.UtcNow))
                    .ForMember(dest => dest.FlowTypeAttribute, opt => opt.Ignore())
                    .ForMember(dest => dest.Flow, opt => opt.Ignore());

                // Configurazione speciale per FlowUserRole -> FlowUserDto
                config.CreateMap<FlowUserRole, FlowUserDto>()
                    .ForMember(dest => dest.UserName, opt => opt.Ignore())
                    .ForMember(dest => dest.UserEmail, opt => opt.Ignore())
                    .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => true));

                // Configurazione speciale per FlowOrganizationUnitRole -> FlowOrganizationUnitRoleDto
                config.CreateMap<FlowOrganizationUnitRole, FlowOrganizationUnitRoleDto>()
                    .ForMember(dest => dest.OrganizationUnitName, opt => opt.Ignore())
                    .ForMember(dest => dest.OrganizationUnitEmail, opt => opt.Ignore());

                // Configurazione speciale per FlowUserDto -> FlowUserRole
                config.CreateMap<FlowUserDto, FlowUserRole>()
                    .ForMember(dest => dest.Flow, opt => opt.Ignore());

                config.CreateMap<FlowUserCreateDto, FlowUserRole>()
                    .ForMember(dest => dest.Flow, opt => opt.Ignore());

                config.CreateMap<DocumentFile, DocumentFileDto>().ReverseMap();
                config.CreateMap<BinaryObject, BinaryObjectDto>().ReverseMap();
                config.CreateMap<Project, ProjectDto>().ReverseMap();
                config.CreateMap<ProjectDto, ProjectCreateDto>();
                config.CreateMap<Area, AreaDto>().ReverseMap();
                config.CreateMap<ProjectMilestone, ProjectMilestoneDto>().ReverseMap();

                // Mapping per FlowTypeAttribute e FlowTypeSheet
                config.CreateMap<FlowTypeAttribute, FlowTypeAttributeDto>().ReverseMap();
                config.CreateMap<FlowTypeSheet, FlowTypeSheetDto>().ReverseMap();

                config.CreateMap<EnumType, EnumTypeDto>().ReverseMap();
                config.CreateMap<EnumTypeValue, EnumTypeValueDto>().ReverseMap();

                // Mapping per SheetComment
                config.CreateMap<SheetComment, SheetCommentDto>()
                    .ForMember(dest => dest.CreatorName, opt => opt.Ignore())
                    .ForMember(dest => dest.CreatorEmail, opt => opt.Ignore())
                    .ReverseMap()
                    .ForMember(dest => dest.Sheet, opt => opt.Ignore())
                    .ForMember(dest => dest.ParentComment, opt => opt.Ignore());

                config.CreateMap<SheetCommentCreateDto, SheetComment>()
                    .ForMember(dest => dest.Id, opt => opt.MapFrom(src => Guid.NewGuid()))
                    .ForMember(dest => dest.CreationTime, opt => opt.MapFrom(src => DateTime.UtcNow))
                    .ForMember(dest => dest.Sheet, opt => opt.Ignore())
                    .ForMember(dest => dest.ParentComment, opt => opt.Ignore())
                    .ForMember(dest => dest.Replies, opt => opt.Ignore());

                // Mapping per Sheet
                config.CreateMap<Sheet, SheetDto>()
                    .ReverseMap()
                    .ForMember(dest => dest.FlowTypeSheet, opt => opt.Ignore())
                    .ForMember(dest => dest.Flow, opt => opt.Ignore())
                    .ForMember(dest => dest.History, opt => opt.Ignore())
                    .ForMember(dest => dest.Comments, opt => opt.Ignore());

                config.CreateMap<SheetCreateDto, Sheet>()
                    .ForMember(dest => dest.Id, opt => opt.MapFrom(src => Guid.NewGuid()))
                    .ForMember(dest => dest.CreationTime, opt => opt.MapFrom(src => DateTime.UtcNow))
                    .ForMember(dest => dest.FlowTypeSheet, opt => opt.Ignore())
                    .ForMember(dest => dest.Flow, opt => opt.Ignore())
                    .ForMember(dest => dest.History, opt => opt.Ignore())
                    .ForMember(dest => dest.Comments, opt => opt.Ignore());

                // Mapping per User
                config.CreateMap<User, UserDto>()
                    .ForMember(dest => dest.Roles, opt => opt.MapFrom(src =>
                        src.UserRoles != null ? src.UserRoles.Select(ur => ur.Role) : new List<Role>()))
                    .ForMember(dest => dest.OrganizationUnitId, opt => opt.MapFrom(src => src.OrganizationUnitId));

                config.CreateMap<UserCreateDto, User>()
                    .ForMember(dest => dest.Id, opt => opt.MapFrom(src => Guid.NewGuid()))
                    .ForMember(dest => dest.CreationTime, opt => opt.MapFrom(src => DateTime.UtcNow))
                    .ForMember(dest => dest.UserRoles, opt => opt.Ignore());

                config.CreateMap<UserUpdateDto, User>()
                    .ForMember(dest => dest.Username, opt => opt.Ignore())
                    .ForMember(dest => dest.CreationTime, opt => opt.Ignore())
                    .ForMember(dest => dest.LastModificationTime, opt => opt.MapFrom(src => DateTime.UtcNow))
                    .ForMember(dest => dest.UserRoles, opt => opt.Ignore())
                    .ForMember(dest => dest.OrganizationUnitId, opt => opt.MapFrom(src => src.OrganizationUnitId));

                // Mapping per Role e Permission
                config.CreateMap<Role, RoleDto>()
                    .ForMember(dest => dest.Permissions, opt => opt.MapFrom(src =>
                        src.RolePermissions != null ? src.RolePermissions.Select(rp => rp.Permission) : new List<Permission>()));

                config.CreateMap<Permission, PermissionDto>().ReverseMap();

                // Add mappings for UnitType and CommandRole
                config.CreateMap<UnitType, UnitTypeDto>()
                    .ForMember(dest => dest.CommandRoles, opt => opt.MapFrom(src => src.CommandRoles));

                config.CreateMap<UnitTypeDto, UnitType>()
                    .ForMember(dest => dest.CommandRoles, opt => opt.MapFrom(src => src.CommandRoles));

                config.CreateMap<CommandRoleDto, CommandRole>()
                    .ForMember(dest => dest.UnitType, opt => opt.Ignore())
                    .ForMember(dest => dest.CommandAssignment, opt => opt.Ignore());

                // Add reverse mapping for CommandRole -> CommandRoleDto
                config.CreateMap<CommandRole, CommandRoleDto>()
                    .ForMember(dest => dest.UnitTypeId, opt => opt.MapFrom(src => src.UnitTypeId));

                // Add mapping for CommandRoleCreateDto
                config.CreateMap<CommandRoleCreateDto, CommandRole>()
                    .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id == Guid.Empty ? Guid.NewGuid() : src.Id))
                    .ForMember(dest => dest.CreationTime, opt => opt.MapFrom(src => DateTime.UtcNow))
                    .ForMember(dest => dest.UnitType, opt => opt.Ignore())
                    .ForMember(dest => dest.CommandAssignment, opt => opt.Ignore());

                // Add mappings for UnitTypeCreateDto
                config.CreateMap<UnitTypeCreateDto, UnitType>()
                    .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id == Guid.Empty ? Guid.NewGuid() : src.Id))
                    .ForMember(dest => dest.CreationTime, opt => opt.MapFrom(src => DateTime.UtcNow));

                config.CreateMap<CommandRoleCreateDto, CommandRole>()
                    .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id == Guid.Empty ? Guid.NewGuid() : src.Id))
                    .ForMember(dest => dest.CreationTime, opt => opt.MapFrom(src => DateTime.UtcNow))
                    .ForMember(dest => dest.UnitType, opt => opt.Ignore());

                config.CreateMap<OrganizationUnitDto, OrganizationUnit>()
                    .ForMember(dest => dest.UnitType, opt => opt.Ignore())
                    .ForMember(dest => dest.ParentUnit, opt => opt.Ignore())
                    .ForMember(dest => dest.ChildUnits, opt => opt.Ignore())
                    .ForMember(dest => dest.CommandAssignments, opt => opt.Ignore());

                // Add reverse mapping for OrganizationUnit -> OrganizationUnitDto
                config.CreateMap<OrganizationUnit, OrganizationUnitDto>()
                    .ForMember(dest => dest.ChildUnits, opt => opt.MapFrom(src => src.ChildUnits))
                    .ForMember(dest => dest.CommandAssignments, opt => opt.MapFrom(src => src.CommandAssignments))
                    .PreserveReferences();

                config.CreateMap<OrganizationUnitCreateDto, OrganizationUnit>()
                    .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id.HasValue && src.Id.Value != Guid.Empty ? src.Id.Value : Guid.NewGuid()))
                    .ForMember(dest => dest.CreationTime, opt => opt.MapFrom(src => DateTime.UtcNow))
                    .ForMember(dest => dest.UnitType, opt => opt.Ignore())
                    .ForMember(dest => dest.ParentUnit, opt => opt.Ignore())
                    .ForMember(dest => dest.ChildUnits, opt => opt.Ignore())
                    .ForMember(dest => dest.CommandAssignments, opt => opt.Ignore());

                // Update OrganizationUnitCommandAssignment mappings
                config.CreateMap<OrganizationUnitCommandAssignment, OrganizationUnitCommandAssignmentDto>()
                    .ForMember(dest => dest.OrganizationUnitName, opt => opt.MapFrom(src => src.OrganizationUnit != null ? src.OrganizationUnit.Name : null))
                    .ForMember(dest => dest.CommandRoleName, opt => opt.MapFrom(src => src.CommandRoles != null ? src.CommandRoles.Name : null))
                    .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User != null ?
                        (string.IsNullOrEmpty(src.User.FirstName) && string.IsNullOrEmpty(src.User.LastName) ?
                            src.User.Username :
                            $"{src.User.FirstName} {src.User.LastName}").Trim() :
                        null));

                config.CreateMap<OrganizationUnitCommandAssignmentDto, OrganizationUnitCommandAssignment>()
                    .ForMember(dest => dest.OrganizationUnit, opt => opt.Ignore())
                    .ForMember(dest => dest.CommandRoles, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                // Add mapping for OrganizationUnitCommandAssignmentCreateDto
                config.CreateMap<OrganizationUnitCommandAssignmentCreateDto, OrganizationUnitCommandAssignment>()
                    .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id.HasValue && src.Id.Value != Guid.Empty ? src.Id.Value : Guid.NewGuid()))
                    .ForMember(dest => dest.OrganizationUnit, opt => opt.Ignore())
                    .ForMember(dest => dest.CommandRoles, opt => opt.Ignore())
                    .ForMember(dest => dest.User, opt => opt.Ignore());

                // Add mappings for ProjectOrganizationUnitRole
                config.CreateMap<ProjectOrganizationUnitRole, ProjectOrganizationUnitRoleDto>()
                    .ForMember(dest => dest.ProjectName, opt => opt.MapFrom(src => src.Project != null ? src.Project.Name : null))
                    .ForMember(dest => dest.OrganizationUnitName, opt => opt.MapFrom(src => src.OrganizationUnit != null ? src.OrganizationUnit.Name : null))
                    .ForMember(dest => dest.OrganizationUnitEmail, opt => opt.MapFrom(src => src.OrganizationUnit != null ? src.OrganizationUnit.Email : null));



                // Base mappings
                config.CreateMap<BaseEntity, BaseDto>();
            });

            return services;
        }
    }
}