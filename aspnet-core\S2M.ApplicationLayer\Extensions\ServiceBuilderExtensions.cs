using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using S2M.ApplicationLayer.Handlers;
using S2M.ApplicationLayer.Services.Implementations;
using S2M.ApplicationLayer.Services.Interfaces;
using S2M.InfrastructureLayer.Extensions;
using System.Net.Http;

namespace S2M.ApplicationLayer.Extensions
{
    /// <summary>
    /// Estensioni per la configurazione dei servizi dell'applicazione
    /// </summary>
    public static class ServiceBuilderExtensions
    {
        /// <summary>
        /// Aggiunge i servizi di utilità al container di dependency injection
        /// </summary>
        /// <param name="builder">Builder dell'applicazione</param>
        /// <returns>Builder dell'applicazione aggiornato</returns>
        public static WebApplicationBuilder AddUtilityServices(this WebApplicationBuilder builder)
        {
            // Registra i servizi dell'applicazione
            builder.Services.AddApplicationServices(builder.Configuration);

            return builder;
        }

        /// <summary>
        /// Aggiunge i servizi dell'applicazione al container di dependency injection
        /// </summary>
        /// <param name="services">Collection di servizi</param>
        /// <param name="configuration">Configurazione dell'applicazione</param>
        /// <returns>Collection di servizi aggiornata</returns>
        public static IServiceCollection AddApplicationServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Registra i servizi dell'infrastructure layer
            services.AddInfrastructureServices(configuration);

            // Registra i servizi di mapping
            services.AddMappingServices();

            // Registra AutoMapper
            services.AddAutoMapperProfiles();

            // Registra i servizi dell'applicazione
            services.AddScoped<IFlowTypeService, FlowTypeService>();
            services.AddScoped<IFlowService, FlowService>();
            services.AddScoped<IFlowUserService, FlowUserService>();
            services.AddScoped<IFlowPermissionService, FlowPermissionService>();
            services.AddScoped<IDocumentFileService, DocumentFileService>();
            services.AddScoped<ISheetCommentService, SheetCommentService>();

            // Registra i nuovi servizi
            services.AddScoped<ISheetService, SheetService>();
            services.AddScoped<IAreaService, AreaService>();
            services.AddScoped<IProjectService, ProjectService>();
            services.AddScoped<IBinaryObjectService, BinaryObjectService>();
            services.AddScoped<IEnumTypeService, EnumTypeService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IUserCacheService, UserCacheService>();
            services.AddScoped<IRoleService, RoleService>();
            services.AddScoped<ICommandRoleService, CommandRoleService>();
            services.AddScoped<IUnitTypeService, UnitTypeService>();
            services.AddScoped<IOrganizationUnitCommandAssignmentService, OrganizationUnitCommandAssignmentService>();
            services.AddScoped<IOrganizationUnitService, OrganizationUnitService>();
            services.AddScoped<IFlowOrganizationUnitRoleService, FlowOrganizationUnitRoleService>();

            // Registra i servizi per Keycloak
            services.AddScoped<ITokenService, TokenService>();
            services.AddScoped<IKeycloakService, KeycloakService>();

            // Registra l'HttpClient per Keycloak
            services.AddHttpClient<IKeycloakService, KeycloakService>(client =>
            {
                client.DefaultRequestHeaders.Add("Accept", "application/json");
            })
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                ClientCertificateOptions = ClientCertificateOption.Manual,
                ServerCertificateCustomValidationCallback = delegate { return true; }
            })
            .AddHttpMessageHandler<ProtectedApiBearerTokenHandler>();

            // Registra l'HttpClient per ignorare SSL
            services.AddHttpClient("ignoreSSL").ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                ClientCertificateOptions = ClientCertificateOption.Manual,
                ServerCertificateCustomValidationCallback = delegate { return true; }
            });

            return services;
        }
    }
}