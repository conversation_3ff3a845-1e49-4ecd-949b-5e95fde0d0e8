using S2M.ApplicationLayer.Dto;
using S2M.ApplicationLayer.Services.Interfaces;
using S2M.DomainModelLayer.Entities;
using S2M.DomainModelLayer.Enums;
using S2M.InfrastructureLayer.Repository;
using S2M.InfrastructureLayer.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace S2M.ApplicationLayer.Services.Implementations
{
    /// <summary>
    /// Implementazione del servizio dei ruoli delle unità organizzative nei flussi
    /// </summary>
    public class FlowOrganizationUnitRoleService : IFlowOrganizationUnitRoleService
    {
        private readonly IFlowOrganizationUnitRoleRepository _flowOrganizationUnitRoleRepository;
        private readonly IFlowRepository _flowRepository;
        private readonly IOrganizationUnitRepository _organizationUnitRepository;
        private readonly IMappingService _mappingService;
        private readonly ICurrentUserService _currentUserService;

        /// <summary>
        /// Costruttore
        /// </summary>
        /// <param name="flowOrganizationUnitRoleRepository">Repository dei ruoli delle unità organizzative nei flussi</param>
        /// <param name="flowRepository">Repository dei flussi</param>
        /// <param name="organizationUnitRepository">Repository delle unità organizzative</param>
        /// <param name="mappingService">Servizio di mapping</param>
        /// <param name="currentUserService">Servizio dell'utente corrente</param>
        public FlowOrganizationUnitRoleService(
            IFlowOrganizationUnitRoleRepository flowOrganizationUnitRoleRepository,
            IFlowRepository flowRepository,
            IOrganizationUnitRepository organizationUnitRepository,
            IMappingService mappingService,
            ICurrentUserService currentUserService)
        {
            _flowOrganizationUnitRoleRepository = flowOrganizationUnitRoleRepository;
            _flowRepository = flowRepository;
            _organizationUnitRepository = organizationUnitRepository;
            _mappingService = mappingService;
            _currentUserService = currentUserService;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<FlowOrganizationUnitRoleDto>> GetByFlowIdAsync(Guid flowId)
        {
            var roles = await _flowOrganizationUnitRoleRepository.GetByFlowIdAsync(flowId);
            return _mappingService.MapCollection<FlowOrganizationUnitRole, FlowOrganizationUnitRoleDto>(roles);
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<FlowOrganizationUnitRoleDto>> GetByOrganizationUnitIdAsync(Guid organizationUnitId)
        {
            var roles = await _flowOrganizationUnitRoleRepository.GetByOrganizationUnitIdAsync(organizationUnitId);
            return _mappingService.MapCollection<FlowOrganizationUnitRole, FlowOrganizationUnitRoleDto>(roles);
        }

        /// <inheritdoc/>
        public async Task<FlowOrganizationUnitRoleDto> GetByFlowAndOrganizationUnitAsync(Guid flowId, Guid organizationUnitId)
        {
            var role = await _flowOrganizationUnitRoleRepository.GetByFlowAndOrganizationUnitAsync(flowId, organizationUnitId);
            return _mappingService.Map<FlowOrganizationUnitRole, FlowOrganizationUnitRoleDto>(role);
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<FlowOrganizationUnitRoleDto>> GetPreApproversByFlowIdAsync(Guid flowId)
        {
            var roles = await _flowOrganizationUnitRoleRepository.GetPreApproversByFlowIdAsync(flowId);
            return _mappingService.MapCollection<FlowOrganizationUnitRole, FlowOrganizationUnitRoleDto>(roles);
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<FlowOrganizationUnitRoleDto>> GetCoordinatorsByFlowIdAsync(Guid flowId)
        {
            var roles = await _flowOrganizationUnitRoleRepository.GetCoordinatorsByFlowIdAsync(flowId);
            return _mappingService.MapCollection<FlowOrganizationUnitRole, FlowOrganizationUnitRoleDto>(roles);
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<FlowOrganizationUnitRoleDto>> GetApproversByFlowIdAsync(Guid flowId)
        {
            var roles = await _flowOrganizationUnitRoleRepository.GetApproversByFlowIdAsync(flowId);
            return _mappingService.MapCollection<FlowOrganizationUnitRole, FlowOrganizationUnitRoleDto>(roles);
        }

        /// <inheritdoc/>
        public async Task<FlowOrganizationUnitRoleDto> CreateAsync(Guid flowId, Guid organizationUnitId, FlowOrganizationUnitRoleType role)
        {
            // Verifica che il flusso esista
            var flow = await _flowRepository.GetByIdAsync(flowId);
            if (flow == null)
            {
                throw new ArgumentException($"Il flusso con ID {flowId} non esiste");
            }

            // Verifica che l'unità organizzativa esista
            var organizationUnit = await _organizationUnitRepository.GetByIdAsync(organizationUnitId);
            if (organizationUnit == null)
            {
                throw new ArgumentException($"L'unità organizzativa con ID {organizationUnitId} non esiste");
            }

            // Verifica che non esista già un ruolo per questa combinazione
            var existingRole = await _flowOrganizationUnitRoleRepository.GetByFlowAndOrganizationUnitAsync(flowId, organizationUnitId);
            if (existingRole != null)
            {
                throw new InvalidOperationException($"Esiste già un ruolo per l'unità organizzativa {organizationUnitId} nel flusso {flowId}");
            }

            // Crea il nuovo ruolo
            var newRole = new FlowOrganizationUnitRole
            {
                Id = Guid.NewGuid(),
                FlowId = flowId,
                OrganizationUnitId = organizationUnitId,
                Role = role
            };

            await _flowOrganizationUnitRoleRepository.AddAsync(newRole);
            await _flowOrganizationUnitRoleRepository.SaveChangesAsync();

            // Carica le relazioni per il ruolo appena creato
            newRole = await _flowOrganizationUnitRoleRepository.GetByFlowAndOrganizationUnitAsync(flowId, organizationUnitId);
            return _mappingService.Map<FlowOrganizationUnitRole, FlowOrganizationUnitRoleDto>(newRole);
        }

        /// <inheritdoc/>
        public async Task<FlowOrganizationUnitRoleDto> UpdateAcknowledgeAsync(Guid flowId, Guid organizationUnitId, bool acknowledge)
        {
            var role = await _flowOrganizationUnitRoleRepository.GetByFlowAndOrganizationUnitAsync(flowId, organizationUnitId);
            if (role == null)
            {
                throw new ArgumentException($"Non esiste un ruolo per l'unità organizzativa {organizationUnitId} nel flusso {flowId}");
            }

            role.Acknowledge = acknowledge;
            role.AcknowledgeDate = acknowledge ? DateTime.UtcNow : null;

            await _flowOrganizationUnitRoleRepository.UpdateAsync(role);
            await _flowOrganizationUnitRoleRepository.SaveChangesAsync();

            // Carica le relazioni per il ruolo aggiornato
            role = await _flowOrganizationUnitRoleRepository.GetByFlowAndOrganizationUnitAsync(flowId, organizationUnitId);
            return _mappingService.Map<FlowOrganizationUnitRole, FlowOrganizationUnitRoleDto>(role);
        }

        /// <inheritdoc/>
        public async Task<bool> RemoveAsync(Guid flowId, Guid organizationUnitId)
        {
            var role = await _flowOrganizationUnitRoleRepository.GetByFlowAndOrganizationUnitAsync(flowId, organizationUnitId);
            if (role == null)
            {
                return false;
            }

            await _flowOrganizationUnitRoleRepository.DeleteAsync(role);
            await _flowOrganizationUnitRoleRepository.SaveChangesAsync();

            return true;
        }
    }
} 