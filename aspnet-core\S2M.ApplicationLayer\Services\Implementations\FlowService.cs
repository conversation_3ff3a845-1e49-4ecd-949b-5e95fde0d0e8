using S2M.ApplicationLayer.Dto;
using S2M.ApplicationLayer.Services.Interfaces;
using S2M.DomainModelLayer.Entities;
using S2M.DomainModelLayer.Enums;
using S2M.DomainModelLayer.Constants;
using S2M.InfrastructureLayer.Repository;
using S2M.InfrastructureLayer.Services;
using Action = S2M.DomainModelLayer.Constants.Action;

namespace S2M.ApplicationLayer.Services.Implementations
{
    /// <summary>
    /// Implementazione del servizio dei flussi
    /// </summary>
    public class FlowService : IFlowService
    {
        private readonly IFlowRepository _flowRepository;
        private readonly IFlowTypeRepository _flowTypeRepository;
        private readonly IAttributeRepository _attributeRepository;
        private readonly ISheetRepository _sheetRepository;
        private readonly IFlowTypeService _flowTypeService;
        private readonly IMappingService _mappingService;
        private readonly ICurrentUserService _currentUserService;
        private readonly IFlowUserService _flowUserService;
        private readonly IFlowPermissionService _flowPermissionService;
        private readonly IUserRepository _userRepository;
        private readonly IFlowOrganizationUnitRoleService _flowOrganizationUnitRoleService;

        /// <summary>
        /// Costruttore
        /// </summary>
        /// <param name="flowRepository">Repository dei flussi</param>
        /// <param name="flowTypeRepository">Repository dei tipi di flusso</param>
        /// <param name="attributeRepository">Repository degli attributi</param>
        /// <param name="sheetRepository">Repository degli sheet</param>
        /// <param name="flowTypeService">Servizio dei tipi di flusso</param>
        /// <param name="mappingService">Servizio di mapping</param>
        /// <param name="currentUserService">Servizio dell'utente corrente</param>
        /// <param name="flowUserService">Servizio del Flow User Role</param>
        /// <param name="flowPermissionService">Servizio dei permessi sui flussi</param>
        /// <param name="userRepository">Repository degli utenti</param>
        /// <param name="flowOrganizationUnitRoleService">Servizio dei ruoli delle unità organizzative nei flussi</param>
        public FlowService(
            IFlowRepository flowRepository,
            IFlowTypeRepository flowTypeRepository,
            IAttributeRepository attributeRepository,
            ISheetRepository sheetRepository,
            IFlowTypeService flowTypeService,
            IMappingService mappingService,
            ICurrentUserService currentUserService,
            IFlowUserService flowUserService,
            IFlowPermissionService flowPermissionService,
            IUserRepository userRepository,
            IFlowOrganizationUnitRoleService flowOrganizationUnitRoleService)
        {
            _flowRepository = flowRepository;
            _flowTypeRepository = flowTypeRepository;
            _attributeRepository = attributeRepository;
            _sheetRepository = sheetRepository;
            _flowTypeService = flowTypeService;
            _mappingService = mappingService;
            _currentUserService = currentUserService;
            _flowUserService = flowUserService;
            _flowPermissionService = flowPermissionService;
            _userRepository = userRepository;
            _flowOrganizationUnitRoleService = flowOrganizationUnitRoleService;
        }

        /// <summary>
        /// Converte una stringa in un'istanza di Action
        /// </summary>
        private Action ConvertToAction(string actionName)
        {
            // Cerca l'azione in tutte le classi statiche di UserActions
            var allActions = typeof(UserActions)
                .GetNestedTypes()
                .SelectMany(t => t.GetFields())
                .Where(f => f.FieldType == typeof(Action))
                .Select(f => (Action)f.GetValue(null))
                .FirstOrDefault(a => a.Name == actionName);

            if (allActions == null)
            {
                throw new ArgumentException($"Azione '{actionName}' non trovata", nameof(actionName));
            }

            return allActions;
        }

        /// <summary>
        /// Ottiene tutti i flussi
        /// </summary>
        /// <returns>Lista di flussi</returns>
        public async Task<IEnumerable<FlowDto>> GetAllAsync()
        {
            var flows = await _flowRepository.GetAllWithRelationsAsync(trackChanges: false);
            var flowDtos = _mappingService.MapCollection<Flow, FlowDto>(flows).ToList();

            // Popola le informazioni del creator per ogni flow
            foreach (var flowDto in flowDtos)
            {
                var flow = flows.First(f => f.Id == flowDto.Id);
                if (flow.CreatorId != null)
                {
                    var creator = await _userRepository.GetByIdAsync(flow.CreatorId.Value);
                    if (creator != null)
                    {
                        flowDto.CreatorFirstName = creator.FirstName;
                        flowDto.CreatorLastName = creator.LastName;
                        flowDto.CreatorRank = creator.Rank;
                    }
                }
            }

            return flowDtos;
        }

        /// <summary>
        /// Ottiene un flusso per ID
        /// </summary>
        /// <param name="id">ID del flusso</param>
        /// <returns>Flusso</returns>
        public async Task<FlowDto> GetByIdAsync(Guid id)
        {
            // Usa il metodo che include già tutte le relazioni
            var flow = await _flowRepository.GetWithRelationsAsync(id, trackChanges: false);
            if (flow == null)
            {
                return null;
            }

            var flowDto = _mappingService.Map<Flow, FlowDto>(flow);
            
            // Popola le informazioni del creator
            if (flow.CreatorId != null)
            {
                var creator = await _userRepository.GetByIdAsync(flow.CreatorId.Value);
                if (creator != null)
                {
                    flowDto.CreatorFirstName = creator.FirstName;
                    flowDto.CreatorLastName = creator.LastName;
                    flowDto.CreatorRank = creator.Rank;
                }
            }

            return flowDto;
        }

        /// <summary>
        /// Ottiene i flussi per tipo
        /// </summary>
        /// <param name="flowTypeId">ID del tipo di flusso</param>
        /// <returns>Lista di flussi</returns>
        public async Task<IEnumerable<FlowDto>> GetByFlowTypeIdAsync(Guid flowTypeId)
        {
            var flows = await _flowRepository.GetByFlowTypeIdWithRelationsAsync(flowTypeId, trackChanges: false);
            return _mappingService.MapCollection<Flow, FlowDto>(flows);
        }

        /// <summary>
        /// Ottiene i flussi per unità organizzativa
        /// </summary>
        /// <param name="organizationUnitId">ID dell'unità organizzativa</param>
        /// <returns>Lista di flussi</returns>
        public async Task<IEnumerable<FlowDto>> GetByOrganizationUnitIdAsync(Guid organizationUnitId)
        {
            var flows = await _flowRepository.GetByOrganizationUnitIdWithRelationsAsync(organizationUnitId, trackChanges: false);
            var flowDtos = _mappingService.MapCollection<Flow, FlowDto>(flows).ToList();

            // Popola le informazioni del creator per ogni flow
            foreach (var flowDto in flowDtos)
            {
                var flow = flows.First(f => f.Id == flowDto.Id);
                if (flow.CreatorId != null)
                {
                    var creator = await _userRepository.GetByIdAsync(flow.CreatorId.Value);
                    if (creator != null)
                    {
                        flowDto.CreatorFirstName = creator.FirstName;
                        flowDto.CreatorLastName = creator.LastName;
                        flowDto.CreatorRank = creator.Rank;
                    }
                }
            }

            return flowDtos;
        }

        /// <summary>
        /// Crea un nuovo flusso
        /// </summary>
        /// <param name="flowDto">Dati del flusso</param>
        /// <returns>Flusso creato</returns>
        public async Task<FlowDto> CreateAsync(FlowCreateDto flowDto)
        {
            // Ottiene l'utente corrente
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
            {
                throw new InvalidOperationException("Impossibile determinare l'utente corrente. Impossibile creare il flusso.");
            }

            // Verifica che l'utente sia associato a un'unità organizzativa
            if (currentUser.OrganizationUnitId == null)
            {
                throw new InvalidOperationException($"L'utente corrente '{currentUser.Username}' non è associato a nessuna unità organizzativa. Impossibile creare il flusso.");
            }

            // Verifica che il tipo di flusso esista
            var flowType = await _flowTypeRepository.GetByIdAsync(flowDto.FlowTypeId, trackChanges: false);
            if (flowType == null)
            {
                throw new ArgumentException($"Il tipo di flusso con ID {flowDto.FlowTypeId} non esiste");
            }

            // Genera un nuovo ID se non è stato specificato o è vuoto
            if (flowDto.Id == Guid.Empty)
            {
                flowDto.Id = Guid.NewGuid();
            }

            var flow = _mappingService.Map<FlowCreateDto, Flow>(flowDto);

            // Popola automaticamente OrganizationUnitId con l'unità organizzativa dell'utente creator
            flow.OrganizationUnitId = currentUser.OrganizationUnitId.Value;

            // Crea il flusso
            await _flowRepository.AddAsync(flow);

            // Crea gli attributi associati al flusso
            if (flowDto.Attributes != null && flowDto.Attributes.Count > 0)
            {
                foreach (var attributeDto in flowDto.Attributes)
                {
                    var attribute = new DomainModelLayer.Entities.Attribute
                    {
                        Id = Guid.NewGuid(),
                        FlowId = flow.Id,
                        FlowTypeAttributeId = attributeDto.FlowTypeAttributeId,
                        Value = attributeDto.Value
                    };
                    await _attributeRepository.AddAsync(attribute);
                }
            }

            // Crea automaticamente tutti gli Sheet associati al FlowType
            await CreateSheetsForFlowAsync(flow.Id, flowDto.FlowTypeId);

            var flowUserRole = new FlowUserCreateDto {
                FlowId = flow.Id,
                UserId = flow.CreatorId ?? Guid.Empty,
                IsActive = true,
                Role = DomainModelLayer.Enums.FlowUserRoleType.Owner
            };

            await _flowUserService.CreateAsync(flowUserRole);
            // Carica le relazioni per il flusso appena creato
            flow = await _flowRepository.GetWithRelationsAsync(flow.Id, trackChanges: false);

            return _mappingService.Map<Flow, FlowDto>(flow);
        }

        /// <summary>
        /// Crea automaticamente tutti gli Sheet associati a un Flow basandosi sui FlowTypeSheet del FlowType
        /// </summary>
        /// <param name="flowId">ID del Flow</param>
        /// <param name="flowTypeId">ID del FlowType</param>
        /// <returns>Task completato</returns>
        private async Task CreateSheetsForFlowAsync(Guid flowId, Guid flowTypeId)
        {
            try
            {
                // Ottieni tutti i FlowTypeSheet per questo FlowType
                var flowTypeSheets = await _flowTypeService.GetSheetsByFlowTypeIdAsync(flowTypeId);
                
                if (flowTypeSheets?.Any() != true)
                {
                    return; // Nessun sheet da creare
                }

                // Crea un Sheet per ogni FlowTypeSheet
                foreach (var flowTypeSheet in flowTypeSheets)
                {
                    var sheet = new Sheet
                    {
                        Id = Guid.NewGuid(),
                        Name = flowTypeSheet.Name ?? "Sheet senza nome",
                        FlowId = flowId,
                        FlowTypeSheetId = flowTypeSheet.Id
                    };

                    await _sheetRepository.AddAsync(sheet);
                }

                // Salva tutti i cambiamenti
                await _sheetRepository.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log dell'errore (potresti voler usare un logger qui)
                // Per ora, non interrompiamo la creazione del Flow per errori negli Sheet
                System.Diagnostics.Debug.WriteLine($"Errore nella creazione degli Sheet per il Flow {flowId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Aggiorna un flusso esistente
        /// </summary>
        /// <param name="id">ID del flusso</param>
        /// <param name="flowDto">Dati aggiornati del flusso</param>
        /// <returns>Esito dell'operazione</returns>
        public async Task<bool> UpdateAsync(Guid id, FlowDto flowDto)
        {
            // Verifica che il flusso esista
            var existingFlow = await _flowRepository.GetByIdAsync(id, trackChanges: true);
            if (existingFlow == null)
            {
                return false;
            }

            // Verifica che il tipo di flusso esista
            var flowType = await _flowTypeRepository.GetByIdAsync(flowDto.FlowTypeId, trackChanges: false);
            if (flowType == null)
            {
                throw new ArgumentException($"Il tipo di flusso con ID {flowDto.FlowTypeId} non esiste");
            }

            _mappingService.Map(flowDto, existingFlow);
            
            await _flowRepository.UpdateAsync(existingFlow);

            return true;
        }

        /// <summary>
        /// Elimina un flusso
        /// </summary>
        /// <param name="id">ID del flusso</param>
        /// <returns>Esito dell'operazione</returns>
        public async Task<bool> DeleteAsync(Guid id)
        {
            var flow = await _flowRepository.GetByIdAsync(id, trackChanges: true);
            if (flow == null)
            {
                return false;
            }

            await _flowRepository.DeleteAsync(flow);

            return true;
        }

        /// <summary>
        /// Cambia lo stato di un flusso eseguendo un'azione
        /// </summary>
        /// <param name="id">ID del flusso</param>
        /// <param name="action">Nome dell'azione da eseguire</param>
        /// <returns>True se l'operazione è riuscita, False altrimenti</returns>
        /// <exception cref="UnauthorizedAccessException">Viene lanciata se l'utente non ha i permessi necessari</exception>
        public async Task<bool> ChangeStatusAsync(Guid id, string action)
        {
            // Converti la stringa in Action
            var actionObj = ConvertToAction(action);

            // Verifica dei permessi prima dell'esecuzione
            var permissionCheck = await _flowPermissionService.CanExecuteActionAsync(id, action);

            if (!permissionCheck.CanExecute)
            {
                throw new UnauthorizedAccessException(
                    permissionCheck.Reason ?? "L'utente non ha i permessi necessari per eseguire questa azione"
                );
            }

            var flow = await _flowRepository.GetByIdAsync(id, trackChanges: true);
            if (flow == null) return false;

            var success = flow.TryApplyAction(actionObj);
            if (success)
            {
                await _flowRepository.SaveChangesAsync();
            }

            return success;
        }

        /// <summary>
        /// Invia un flusso per pre-approvazione
        /// </summary>
        /// <param name="flowId">ID del flusso</param>
        /// <returns>Flusso aggiornato</returns>
        public async Task<FlowDto> SendForPreApprovalAsync(Guid flowId)
        {
            // Ottiene il flusso
            var flow = await _flowRepository.GetByIdAsync(flowId, trackChanges: true);
            if (flow == null)
            {
                throw new ArgumentException($"Il flusso con ID {flowId} non esiste");
            }

            // Ottiene l'utente corrente
            var currentUser = await _currentUserService.GetCurrentUserAsync();
            if (currentUser == null)
            {
                throw new InvalidOperationException("Impossibile determinare l'utente corrente");
            }

            // Verifica che l'utente abbia i permessi necessari
            var canExecute = await _flowPermissionService.CanExecuteActionAsync(flowId, UserActions.DocumentPublishing.SendForPreApproval.Name);
            if (!canExecute.CanExecute)
            {
                throw new UnauthorizedAccessException("L'utente non ha i permessi necessari per inviare il flusso per pre-approvazione");
            }

            // Aggiorna lo stato del flusso
            if (!flow.TryApplyAction(ConvertToAction(UserActions.DocumentPublishing.SendForPreApproval.Name)))
            {
                throw new InvalidOperationException("Impossibile inviare il flusso per pre-approvazione: stato non valido");
            }

            // Aggiunge automaticamente l'unità organizzativa dell'utente come pre-approvatore
            if (currentUser.OrganizationUnitId.HasValue)
            {
                await _flowOrganizationUnitRoleService.CreateAsync(flowId, currentUser.OrganizationUnitId.Value, FlowOrganizationUnitRoleType.PreApprover);
            }

            // Salva le modifiche
            await _flowRepository.UpdateAsync(flow);
            await _flowRepository.SaveChangesAsync();

            // Carica le relazioni per il flusso aggiornato
            flow = await _flowRepository.GetWithRelationsAsync(flowId, trackChanges: false);
            return _mappingService.Map<Flow, FlowDto>(flow);
        }
    }
} 