using S2M.ApplicationLayer.Dto;
using S2M.ApplicationLayer.Services.Interfaces;
using S2M.DomainModelLayer.Entities;
using S2M.InfrastructureLayer.Repository;
using S2M.InfrastructureLayer.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using S2M.DomainModelLayer.Enums;

namespace S2M.ApplicationLayer.Services.Implementations
{
    /// <summary>
    /// Implementazione del servizio dei progetti
    /// </summary>
    public class ProjectService : IProjectService
    {
        private readonly IProjectRepository _projectRepository;
        private readonly IAreaRepository _areaRepository;
        private readonly IProjectOrganizationUnitRoleRepository _projectOrgUnitRoleRepository;
        private readonly IOrganizationUnitRepository _organizationUnitRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMappingService _mappingService;

        /// <summary>
        /// Costruttore
        /// </summary>
        /// <param name="projectRepository">Repository dei progetti</param>
        /// <param name="areaRepository">Repository delle aree</param>
        /// <param name="projectOrgUnitRoleRepository">Repository dei ruoli delle unità organizzative nei progetti</param>
        /// <param name="organizationUnitRepository">Repository delle unità organizzative</param>
        /// <param name="userRepository">Repository degli utenti</param>
        /// <param name="mappingService">Servizio di mapping</param>
        public ProjectService(
            IProjectRepository projectRepository,
            IAreaRepository areaRepository,
            IProjectOrganizationUnitRoleRepository projectOrgUnitRoleRepository,
            IOrganizationUnitRepository organizationUnitRepository,
            IUserRepository userRepository,
            IMappingService mappingService)
        {
            _projectRepository = projectRepository;
            _areaRepository = areaRepository;
            _projectOrgUnitRoleRepository = projectOrgUnitRoleRepository;
            _organizationUnitRepository = organizationUnitRepository;
            _userRepository = userRepository;
            _mappingService = mappingService;
        }

        /// <summary>
        /// Ottiene tutti i progetti
        /// </summary>
        /// <returns>Lista di progetti</returns>
        public async Task<IEnumerable<ProjectDto>> GetAllAsync()
        {
            var projects = await _projectRepository.GetAllAsync();
            var projectDtos = new List<ProjectDto>();

            foreach (var project in projects)
            {
                var projectDto = _mappingService.Map<Project, ProjectDto>(project);

                // Carica manualmente owner e collaboratori
                var owner = await _projectOrgUnitRoleRepository.GetOwnerByProjectIdAsync(project.Id);
                if (owner != null)
                {
                    projectDto.Owner = _mappingService.Map<OrganizationUnit, OrganizationUnitDto>(owner.OrganizationUnit);
                }

                var collaborators = await _projectOrgUnitRoleRepository.GetCollaboratorsByProjectIdAsync(project.Id);
                if (collaborators != null && collaborators.Any())
                {
                    projectDto.Collaborators = collaborators
                        .Select(c => _mappingService.Map<OrganizationUnit, OrganizationUnitDto>(c.OrganizationUnit))
                        .ToList();
                }
                else
                {
                    projectDto.Collaborators = new List<OrganizationUnitDto>();
                }

                projectDtos.Add(projectDto);
            }

            return projectDtos;
        }

        /// <summary>
        /// Ottiene un progetto per ID
        /// </summary>
        /// <param name="id">ID del progetto</param>
        /// <returns>Progetto</returns>
        public async Task<ProjectDto> GetByIdAsync(Guid id)
        {
            var project = await _projectRepository.GetWithMilestonesAsync(id);
            if (project == null)
                return null;

            var projectDto = _mappingService.Map<Project, ProjectDto>(project);

            // Carica manualmente owner e collaboratori
            var owner = await _projectOrgUnitRoleRepository.GetOwnerByProjectIdAsync(id);
            if (owner != null)
            {
                projectDto.Owner = _mappingService.Map<OrganizationUnit, OrganizationUnitDto>(owner.OrganizationUnit);
            }

            var collaborators = await _projectOrgUnitRoleRepository.GetCollaboratorsByProjectIdAsync(id);
            if (collaborators != null && collaborators.Any())
            {
                projectDto.Collaborators = collaborators
                    .Select(c => _mappingService.Map<OrganizationUnit, OrganizationUnitDto>(c.OrganizationUnit))
                    .ToList();
            }
            else
            {
                projectDto.Collaborators = new List<OrganizationUnitDto>();
            }

            return projectDto;
        }

        /// <summary>
        /// Ottiene i progetti per area
        /// </summary>
        /// <param name="areaId">ID dell'area</param>
        /// <returns>Lista di progetti</returns>
        public async Task<IEnumerable<ProjectDto>> GetByAreaIdAsync(Guid areaId)
        {
            var projects = await _projectRepository.GetByAreaIdAsync(areaId);
            var projectDtos = new List<ProjectDto>();

            foreach (var project in projects)
            {
                var projectDto = _mappingService.Map<Project, ProjectDto>(project);

                // Carica manualmente owner e collaboratori
                var owner = await _projectOrgUnitRoleRepository.GetOwnerByProjectIdAsync(project.Id);
                if (owner != null)
                {
                    projectDto.Owner = _mappingService.Map<OrganizationUnit, OrganizationUnitDto>(owner.OrganizationUnit);
                }

                var collaborators = await _projectOrgUnitRoleRepository.GetCollaboratorsByProjectIdAsync(project.Id);
                if (collaborators != null && collaborators.Any())
                {
                    projectDto.Collaborators = collaborators
                        .Select(c => _mappingService.Map<OrganizationUnit, OrganizationUnitDto>(c.OrganizationUnit))
                        .ToList();
                }
                else
                {
                    projectDto.Collaborators = new List<OrganizationUnitDto>();
                }

                projectDtos.Add(projectDto);
            }

            return projectDtos;
        }

        /// <summary>
        /// Crea un nuovo progetto
        /// </summary>
        /// <param name="projectCreateDto">Dati del progetto</param>
        /// <returns>Progetto creato</returns>
        public async Task<ProjectDto> CreateAsync(ProjectCreateDto projectCreateDto)
        {
            // Valida che l'owner esista
            var ownerUnit = await _organizationUnitRepository.GetByIdAsync(projectCreateDto.OwnerId);
            if (ownerUnit == null)
            {
                throw new ArgumentException($"L'unità organizzativa owner con ID {projectCreateDto.OwnerId} non esiste");
            }

            // Valida che tutti i collaboratori esistano
            if (projectCreateDto.CollaboratorIds != null && projectCreateDto.CollaboratorIds.Count > 0)
            {
                foreach (var collaboratorId in projectCreateDto.CollaboratorIds)
                {
                    var collaboratorUnit = await _organizationUnitRepository.GetByIdAsync(collaboratorId);
                    if (collaboratorUnit == null)
                    {
                        throw new ArgumentException($"L'unità organizzativa collaboratore con ID {collaboratorId} non esiste");
                    }
                }
            }

            var project = new Project
            {
                Id = Guid.NewGuid(),
                Name = projectCreateDto.Name,
                AreaId = projectCreateDto.AreaId,
                EndState = projectCreateDto.EndState,
                Subtitle = projectCreateDto.Subtitle,
                Summary = projectCreateDto.Summary
            };

            await _projectRepository.AddAsync(project);

            // Aggiungi l'owner (sempre obbligatorio) usando il repository
            var ownerRole = new ProjectOrganizationUnitRole
            {
                Id = Guid.NewGuid(),
                ProjectId = project.Id,
                OrganizationUnitId = projectCreateDto.OwnerId,
                UnitRole = ProjectOrganizationUnitRoleType.Owner
            };
            await _projectOrgUnitRoleRepository.AddAsync(ownerRole);
            
            // Aggiungi i collaboratori se specificati usando il repository
            if (projectCreateDto.CollaboratorIds != null && projectCreateDto.CollaboratorIds.Count > 0)
            {
                foreach(var collaboratorId in projectCreateDto.CollaboratorIds)
                {
                    var collaboratorRole = new ProjectOrganizationUnitRole
                    {
                        Id = Guid.NewGuid(),
                        ProjectId = project.Id,
                        OrganizationUnitId = collaboratorId,
                        UnitRole = ProjectOrganizationUnitRoleType.Collaborator
                    };
                    await _projectOrgUnitRoleRepository.AddAsync(collaboratorRole);
                }
            }

            // Carica i dati completi del progetto
            var projectDto = _mappingService.Map<Project, ProjectDto>(project);

            // Carica manualmente owner e collaboratori
            projectDto.Owner = _mappingService.Map<OrganizationUnit, OrganizationUnitDto>(ownerUnit);

            if (projectCreateDto.CollaboratorIds != null && projectCreateDto.CollaboratorIds.Count > 0)
            {
                var collaborators = await _projectOrgUnitRoleRepository.GetCollaboratorsByProjectIdAsync(project.Id);
                if (collaborators != null && collaborators.Any())
                {
                    projectDto.Collaborators = collaborators
                        .Select(c => _mappingService.Map<OrganizationUnit, OrganizationUnitDto>(c.OrganizationUnit))
                        .ToList();
                }
                else
                {
                    projectDto.Collaborators = new List<OrganizationUnitDto>();
                }
            }
            else
            {
                projectDto.Collaborators = new List<OrganizationUnitDto>();
            }

            return projectDto;
        }

        /// <summary>
        /// Aggiorna un progetto esistente
        /// </summary>
        /// <param name="id">ID del progetto</param>
        /// <param name="projectDto">Dati aggiornati del progetto</param>
        /// <returns>Esito dell'operazione</returns>
        public async Task<bool> UpdateAsync(Guid id, ProjectCreateDto projectDto)
        {
            var project = await _projectRepository.GetByIdAsync(id);
            if (project == null)
                return false;
            
            project.Name = projectDto.Name;
            project.AreaId = projectDto.AreaId;
            project.Subtitle = projectDto.Subtitle;
            project.EndState = projectDto.EndState;
            project.Summary = projectDto.Summary;

            await _projectRepository.UpdateAsync(project);

            // Aggiorna i ruoli delle unità organizzative
            await UpdateProjectOrganizationUnitRolesAsync(id, projectDto.OwnerId, projectDto.CollaboratorIds);

            return true;
        }

        /// <summary>
        /// Elimina un progetto
        /// </summary>
        /// <param name="id">ID del progetto</param>
        /// <returns>Esito dell'operazione</returns>
        public async Task<bool> DeleteAsync(Guid id)
        {
            // Otteniamo il progetto con tutte le sue milestone
            var project = await _projectRepository.GetWithMilestonesAsync(id, trackChanges: true);
            if (project == null)
            {
                return false;
            }

            // Verifichiamo se ci sono milestone completate
            if (project.Milestones != null && project.Milestones.Any(m => m.Checked))
            {
                var completedCount = project.Milestones.Count(m => m.Checked);
                throw new InvalidOperationException($"Impossibile eliminare il progetto '{project.Name}' perché contiene {completedCount} milestone completate. Annulla prima le milestone completate.");
            }

            // Rimuovi tutti i ruoli prima di eliminare il progetto
            await _projectOrgUnitRoleRepository.RemoveByProjectIdAsync(id);

            return await _projectRepository.DeleteAsync(id);
        }

        /// <summary>
        /// Aggiunge una milestone a un progetto usando il DTO di creazione
        /// </summary>
        /// <param name="milestoneCreateDto">Dati per la creazione della milestone</param>
        /// <returns>Milestone aggiunta</returns>
        public async Task<ProjectMilestoneDto> AddMilestoneAsync(Guid projectId, ProjectMilestoneCreateDto milestoneCreateDto)
        {
            // Verifica che il progetto esista (senza tracciamento per evitare problemi di concorrenza)
            var project = await _projectRepository.GetWithMilestonesAsync(projectId, trackChanges: false);
            if (project == null)
            {
                throw new ArgumentException($"Il progetto con ID {projectId} non esiste");
            }

            // Crea una nuova milestone
            var milestone = new ProjectMilestone
            {
                Id = Guid.NewGuid(),
                ProjectId = projectId,
                Name = milestoneCreateDto.Name,
                Description = milestoneCreateDto.Description,
                Checked = false,
                Order = (project.Milestones?.Count ?? 0) + 1
            };

            // Aggiungi la milestone direttamente al database
            await _projectRepository.AddMilestoneAsync(milestone);

            return _mappingService.Map<ProjectMilestone, ProjectMilestoneDto>(milestone);
        }

        public async Task<ProjectMilestoneDto> UpdateMilestoneAsync(Guid projectId, Guid milestoneId, ProjectMilestoneCreateDto milestoneUpdateDto)
        {
            var project = await _projectRepository.GetByIdAsync(projectId);
            if (project == null)
            {
                throw new ArgumentException($"Il progetto con ID {projectId} non esiste");
            }

            var milestone = project.Milestones?.FirstOrDefault(m => m.Id == milestoneId);
            if (milestone == null)
            {
                throw new ArgumentException($"La milestone con ID {milestoneId} non esiste");
            }

            // Aggiorna i dati della milestone
            milestone.Name = milestoneUpdateDto.Name;
            milestone.Description = milestoneUpdateDto.Description;
            // Non aggiorniamo Checked e Order, poiché gestiti in altre API

            // Aggiorna il progetto nel database
            await _projectRepository.UpdateAsync(project);

            return _mappingService.Map<ProjectMilestone, ProjectMilestoneDto>(milestone);
        }

        public async Task<bool> DeleteMilestoneAsync(Guid projectId, Guid milestoneId)
        {

            var project = await _projectRepository.GetByIdAsync(projectId);
            if (project == null)
            {
                throw new ArgumentException($"Il progetto con ID {projectId} non esiste");
            }

            var milestone = project.Milestones?.FirstOrDefault(m => m.Id == milestoneId);
            if (project.Milestones == null || milestone == null)
            {
                throw new ArgumentException($"La milestone con ID {milestoneId} non esiste");
            }

            project.Milestones.Remove(milestone);

            // Aggiorna il progetto nel database
            await _projectRepository.UpdateAsync(project);

            return true;
        }

        /// <summary>
        /// Imposta lo stato di completamento di una milestone
        /// </summary>
        /// <param name="dto">DTO con ID della milestone e stato di completamento</param>
        /// <returns>Esito dell'operazione</returns>
        public async Task<bool> SetMilestoneCheckedAsync(Guid projectId, Guid milestoneId, ProjectMilestoneSetCheckedDto dto)
        {
            var project = await _projectRepository.GetWithMilestonesAsync(projectId, trackChanges: true);
            if (project == null)
            {
                return false;
            }

            var milestone = project?.Milestones?.FirstOrDefault(m => m.Id == milestoneId);
            if (milestone == null)
            {
                return false;
            }

            // Aggiorna lo stato della milestone
            milestone.Checked = dto.Checked;
            
            // Aggiorna il progetto nel database
            await _projectRepository.UpdateAsync(project);
            
            return true;
        }

        /// <summary>
        /// Riordina le milestone di un progetto
        /// </summary>
        /// <param name="projectId">ID del progetto</param>
        /// <param name="dto">DTO con l'ordine delle milestone</param>
        /// <returns>Esito dell'operazione</returns>
        public async Task<bool> ReorderMilestonesAsync(Guid projectId, ProjectMilestoneReorderDto dto)
        {
            var project = await _projectRepository.GetWithMilestonesAsync(projectId, true);
            if (project == null || project.Milestones == null || !project.Milestones.Any())
                return false;

            // Verifica che tutte le milestone nel DTO appartengano al progetto
            var projectMilestoneIds = project.Milestones.Select(m => m.Id).ToHashSet();
            if (!dto.MilestoneIds.All(id => projectMilestoneIds.Contains(id)))
                return false;

            // Aggiorna l'ordine delle milestone
            for (int i = 0; i < dto.MilestoneIds.Count; i++)
            {
                var milestone = project.Milestones.First(m => m.Id == dto.MilestoneIds[i]);
                milestone.Order = i;
            }

            await _projectRepository.UpdateAsync(project);
            return true;
        }

        /// <summary>
        /// Aggiorna i ruoli delle unità organizzative per un progetto
        /// </summary>
        /// <param name="projectId">ID del progetto</param>
        /// <param name="ownerId">ID dell'owner</param>
        /// <param name="collaboratorIds">Lista degli ID dei collaboratori</param>
        /// <returns>Task</returns>
        private async Task UpdateProjectOrganizationUnitRolesAsync(Guid projectId, Guid ownerId, List<Guid>? collaboratorIds)
        {
            // Valida che l'owner esista
            var ownerUnit = await _organizationUnitRepository.GetByIdAsync(ownerId);
            if (ownerUnit == null)
            {
                throw new ArgumentException($"L'unità organizzativa owner con ID {ownerId} non esiste");
            }

            // Valida che tutti i collaboratori esistano
            if (collaboratorIds != null && collaboratorIds.Count > 0)
            {
                foreach (var collaboratorId in collaboratorIds)
                {
                    var collaboratorUnit = await _organizationUnitRepository.GetByIdAsync(collaboratorId);
                    if (collaboratorUnit == null)
                    {
                        throw new ArgumentException($"L'unità organizzativa collaboratore con ID {collaboratorId} non esiste");
                    }
                }
            }

            // Ottieni tutti i ruoli attuali
            var currentRoles = await _projectOrgUnitRoleRepository.GetByProjectIdAsync(projectId, trackChanges: true);
            var currentOwner = currentRoles.FirstOrDefault(r => r.UnitRole == ProjectOrganizationUnitRoleType.Owner);
            var currentCollaborators = currentRoles.Where(r => r.UnitRole == ProjectOrganizationUnitRoleType.Collaborator).ToList();

            // Gestisci l'owner (sempre obbligatorio)
            if (currentOwner?.OrganizationUnitId != ownerId)
            {
                // Rimuovi l'owner attuale se esiste
                if (currentOwner != null)
                {
                    await _projectOrgUnitRoleRepository.RemoveByProjectAndOrganizationUnitAsync(projectId, currentOwner.OrganizationUnitId);
                }

                // Rimuovi il nuovo owner dai collaboratori se presente
                var newOwnerAsCollaborator = currentCollaborators.FirstOrDefault(c => c.OrganizationUnitId == ownerId);
                if (newOwnerAsCollaborator != null)
                {
                    await _projectOrgUnitRoleRepository.RemoveByProjectAndOrganizationUnitAsync(projectId, ownerId);
                }

                // Aggiungi il nuovo owner
                var newOwner = new ProjectOrganizationUnitRole
                {
                    Id = Guid.NewGuid(),
                    ProjectId = projectId,
                    OrganizationUnitId = ownerId,
                    UnitRole = ProjectOrganizationUnitRoleType.Owner
                };
                await _projectOrgUnitRoleRepository.AddAsync(newOwner);
            }

            // Gestisci i collaboratori
            var currentCollaboratorIds = currentCollaborators.Select(c => c.OrganizationUnitId).ToHashSet();
            var newCollaboratorIds = (collaboratorIds ?? new List<Guid>())
                .Where(id => id != ownerId) // Escludi l'owner dai collaboratori
                .ToHashSet();

            // Rimuovi i collaboratori che non sono più presenti
            var collaboratorsToRemove = currentCollaboratorIds.Except(newCollaboratorIds);
            foreach (var collaboratorId in collaboratorsToRemove)
            {
                await _projectOrgUnitRoleRepository.RemoveByProjectAndOrganizationUnitAsync(projectId, collaboratorId);
            }

            // Aggiungi i nuovi collaboratori
            var collaboratorsToAdd = newCollaboratorIds.Except(currentCollaboratorIds);
            foreach (var collaboratorId in collaboratorsToAdd)
            {
                var newCollaborator = new ProjectOrganizationUnitRole
                {
                    Id = Guid.NewGuid(),
                    ProjectId = projectId,
                    OrganizationUnitId = collaboratorId,
                    UnitRole = ProjectOrganizationUnitRoleType.Collaborator
                };
                await _projectOrgUnitRoleRepository.AddAsync(newCollaborator);
            }
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<UserDto>> GetProjectUsersAsync(Guid projectId)
        {
            // Ottieni i ruoli delle UO del progetto
            var projectRoles = await _projectOrgUnitRoleRepository.GetByProjectIdAsync(projectId);
            if (projectRoles == null || !projectRoles.Any())
            {
                return new List<UserDto>();
            }

            // Estrai gli ID delle UO (owner e collaboratori)
            var orgUnitIds = projectRoles.Select(r => r.OrganizationUnitId).ToList();

            // Ottieni tutti gli utenti attivi di queste UO
            var users = await _userRepository.GetAsync(u => 
                u.IsActive != false && 
                u.OrganizationUnitId.HasValue && 
                orgUnitIds.Contains(u.OrganizationUnitId.Value)
            );

            // Mappa gli utenti in DTO
            return _mappingService.MapCollection<User, UserDto>(users);
        }
    }
} 