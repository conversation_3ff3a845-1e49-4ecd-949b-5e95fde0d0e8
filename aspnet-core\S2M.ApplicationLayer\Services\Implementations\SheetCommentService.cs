using S2M.ApplicationLayer.Dto;
using S2M.ApplicationLayer.Services.Interfaces;
using S2M.DomainModelLayer.Entities;
using S2M.InfrastructureLayer.Repository;
using S2M.InfrastructureLayer.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace S2M.ApplicationLayer.Services.Implementations
{
    /// <summary>
    /// Implementazione del servizio dei commenti sui fogli
    /// </summary>
    public class SheetCommentService : ISheetCommentService
    {
        private readonly ISheetCommentRepository _sheetCommentRepository;
        private readonly IUserRepository _userRepository;
        private readonly IMappingService _mappingService;
        private readonly ICurrentUserService _currentUserService;

        /// <summary>
        /// Costruttore
        /// </summary>
        /// <param name="sheetCommentRepository">Repository dei commenti sui fogli</param>
        /// <param name="userRepository">Repository degli utenti</param>
        /// <param name="mappingService">Servizio di mapping</param>
        /// <param name="currentUserService">Servizio dell'utente corrente</param>
        public SheetCommentService(
            ISheetCommentRepository sheetCommentRepository,
            IUserRepository userRepository,
            IMappingService mappingService,
            ICurrentUserService currentUserService)
        {
            _sheetCommentRepository = sheetCommentRepository;
            _userRepository = userRepository;
            _mappingService = mappingService;
            _currentUserService = currentUserService;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<SheetCommentDto>> GetBySheetIdAsync(Guid sheetId)
        {
            var comments = await _sheetCommentRepository.GetBySheetIdAsync(sheetId);
            var commentDtos = _mappingService.MapCollection<SheetComment, SheetCommentDto>(comments);
            
            // Arricchisci con i dati dell'utente
            await EnrichWithUserDataAsync(commentDtos);
            
            return commentDtos;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<SheetCommentDto>> GetBySheetIdAndRefIdAsync(Guid sheetId, string refId)
        {
            var comments = await _sheetCommentRepository.GetBySheetIdAndRefIdAsync(sheetId, refId);
            var commentDtos = _mappingService.MapCollection<SheetComment, SheetCommentDto>(comments);
            
            // Arricchisci con i dati dell'utente
            await EnrichWithUserDataAsync(commentDtos);
            
            return commentDtos;
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<SheetCommentDto>> GetMainCommentsWithRepliesAsync(Guid sheetId)
        {
            var comments = await _sheetCommentRepository.GetMainCommentsAsync(sheetId);
            var commentDtos = _mappingService.MapCollection<SheetComment, SheetCommentDto>(comments);
            
            // Arricchisci con i dati dell'utente
            await EnrichWithUserDataAsync(commentDtos);
            
            return commentDtos;
        }

        /// <inheritdoc/>
        public async Task<SheetCommentDto> GetByIdAsync(Guid id)
        {
            var comment = await _sheetCommentRepository.GetByIdAsync(id, trackChanges: false);
            if (comment == null)
            {
                return null;
            }

            var commentDto = _mappingService.Map<SheetComment, SheetCommentDto>(comment);
            
            // Arricchisci con i dati dell'utente
            await EnrichWithUserDataAsync(new[] { commentDto });
            
            return commentDto;
        }

        /// <inheritdoc/>
        public async Task<SheetCommentDto> GetWithRepliesAsync(Guid commentId)
        {
            var comment = await _sheetCommentRepository.GetWithRepliesAsync(commentId);
            if (comment == null)
            {
                return null;
            }

            var commentDto = _mappingService.Map<SheetComment, SheetCommentDto>(comment);
            
            // Arricchisci con i dati dell'utente
            await EnrichWithUserDataAsync(new[] { commentDto });
            
            return commentDto;
        }

        /// <inheritdoc/>
        public async Task<SheetCommentDto> CreateAsync(SheetCommentCreateDto commentDto)
        {
            // Genera un nuovo ID
            var commentId = Guid.NewGuid();
            
            var comment = new SheetComment
            {
                Id = commentId,
                SheetId = commentDto.SheetId,
                RefId = commentDto.RefId,
                Comment = commentDto.Comment,
                ParentCommentId = commentDto.ParentCommentId,
                FlowStatus = commentDto.FlowStatus
            };

            // Il CreatorId verrà impostato automaticamente dal repository
            await _sheetCommentRepository.AddAsync(comment);

            // Ricarica il commento con le relazioni
            var createdComment = await _sheetCommentRepository.GetByIdAsync(commentId, trackChanges: false);
            var createdCommentDto = _mappingService.Map<SheetComment, SheetCommentDto>(createdComment);
            
            // Arricchisci con i dati dell'utente
            await EnrichWithUserDataAsync(new[] { createdCommentDto });
            
            return createdCommentDto;
        }

        /// <inheritdoc/>
        public async Task<bool> UpdateAsync(SheetCommentUpdateDto commentDto)
        {
            var existingComment = await _sheetCommentRepository.GetByIdAsync(commentDto.Id, trackChanges: true);
            if (existingComment == null)
            {
                return false;
            }

            // Ottieni l'utente corrente
            var currentUser = await _currentUserService.GetCurrentUserAsync();

            // Verifica che l'utente corrente sia il creatore del commento
            if (existingComment.CreatorId != currentUser.Id)
            {
                return false;
            }

            // Aggiorna solo i campi modificabili
            existingComment.Comment = commentDto.Comment;
            existingComment.FlowStatus = commentDto.FlowStatus;

            await _sheetCommentRepository.UpdateAsync(existingComment);
            return true;
        }

        /// <inheritdoc/>
        public async Task<bool> DeleteAsync(Guid id)
        {
            var comment = await _sheetCommentRepository.GetByIdAsync(id, trackChanges: true);
            if (comment == null)
            {
                return false;
            }

            // Ottieni l'utente corrente
            var currentUser = await _currentUserService.GetCurrentUserAsync();

            // Verifica che l'utente corrente sia il creatore del commento
            if (comment.CreatorId != currentUser.Id)
            {
                return false;
            }

            await _sheetCommentRepository.DeleteAsync(comment);
            return true;
        }

        /// <inheritdoc/>
        public async Task<bool> HasRepliesAsync(Guid commentId)
        {
            return await _sheetCommentRepository.HasRepliesAsync(commentId);
        }

        /// <summary>
        /// Arricchisce i DTO dei commenti con i dati degli utenti
        /// </summary>
        /// <param name="commentDtos">Lista di DTO dei commenti</param>
        private async Task EnrichWithUserDataAsync(IEnumerable<SheetCommentDto> commentDtos)
        {
            var userIds = commentDtos
                .Where(c => c.CreatorId.HasValue)
                .Select(c => c.CreatorId.Value)
                .Distinct()
                .ToList();

            if (!userIds.Any())
                return;

            // Carica gli utenti uno per uno (non c'è GetByIdsAsync disponibile)
            var userDict = new Dictionary<Guid, User>();
            foreach (var userId in userIds)
            {
                var user = await _userRepository.GetByIdAsync(userId, trackChanges: false);
                if (user != null)
                {
                    userDict[userId] = user;
                }
            }

            foreach (var commentDto in commentDtos)
            {
                if (commentDto.CreatorId.HasValue && userDict.TryGetValue(commentDto.CreatorId.Value, out var user))
                {
                    commentDto.CreatorName = $"{user.FirstName} {user.LastName}".Trim();
                    commentDto.CreatorEmail = user.Email;
                }

                // Arricchisci anche le risposte ricorsivamente
                if (commentDto.Replies?.Any() == true)
                {
                    await EnrichWithUserDataAsync(commentDto.Replies);
                }
            }
        }
    }
} 