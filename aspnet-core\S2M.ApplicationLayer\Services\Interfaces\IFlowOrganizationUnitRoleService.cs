using S2M.ApplicationLayer.Dto;
using S2M.DomainModelLayer.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace S2M.ApplicationLayer.Services.Interfaces
{
    /// <summary>
    /// Interfaccia per il servizio dei ruoli delle unità organizzative nei flussi
    /// </summary>
    public interface IFlowOrganizationUnitRoleService
    {
        /// <summary>
        /// Ottiene tutti i ruoli per un flusso specifico
        /// </summary>
        /// <param name="flowId">ID del flusso</param>
        /// <returns>Lista dei ruoli</returns>
        Task<IEnumerable<FlowOrganizationUnitRoleDto>> GetByFlowIdAsync(Guid flowId);

        /// <summary>
        /// Ottiene tutti i ruoli per un'unità organizzativa specifica
        /// </summary>
        /// <param name="organizationUnitId">ID dell'unità organizzativa</param>
        /// <returns>Lista dei ruoli</returns>
        Task<IEnumerable<FlowOrganizationUnitRoleDto>> GetByOrganizationUnitIdAsync(Guid organizationUnitId);

        /// <summary>
        /// Ottiene un ruolo specifico per flusso e unità organizzativa
        /// </summary>
        /// <param name="flowId">ID del flusso</param>
        /// <param name="organizationUnitId">ID dell'unità organizzativa</param>
        /// <returns>Ruolo se trovato</returns>
        Task<FlowOrganizationUnitRoleDto> GetByFlowAndOrganizationUnitAsync(Guid flowId, Guid organizationUnitId);

        /// <summary>
        /// Ottiene tutti i ruoli di pre-approvazione per un flusso
        /// </summary>
        /// <param name="flowId">ID del flusso</param>
        /// <returns>Lista dei ruoli di pre-approvazione</returns>
        Task<IEnumerable<FlowOrganizationUnitRoleDto>> GetPreApproversByFlowIdAsync(Guid flowId);

        /// <summary>
        /// Ottiene tutti i ruoli di coordinamento per un flusso
        /// </summary>
        /// <param name="flowId">ID del flusso</param>
        /// <returns>Lista dei ruoli di coordinamento</returns>
        Task<IEnumerable<FlowOrganizationUnitRoleDto>> GetCoordinatorsByFlowIdAsync(Guid flowId);

        /// <summary>
        /// Ottiene tutti i ruoli di approvazione per un flusso
        /// </summary>
        /// <param name="flowId">ID del flusso</param>
        /// <returns>Lista dei ruoli di approvazione</returns>
        Task<IEnumerable<FlowOrganizationUnitRoleDto>> GetApproversByFlowIdAsync(Guid flowId);

        /// <summary>
        /// Crea un nuovo ruolo per un'unità organizzativa in un flusso
        /// </summary>
        /// <param name="flowId">ID del flusso</param>
        /// <param name="organizationUnitId">ID dell'unità organizzativa</param>
        /// <param name="role">Ruolo da assegnare</param>
        /// <returns>Ruolo creato</returns>
        Task<FlowOrganizationUnitRoleDto> CreateAsync(Guid flowId, Guid organizationUnitId, FlowOrganizationUnitRoleType role);

        /// <summary>
        /// Aggiorna lo stato di riconoscimento di un ruolo
        /// </summary>
        /// <param name="flowId">ID del flusso</param>
        /// <param name="organizationUnitId">ID dell'unità organizzativa</param>
        /// <param name="acknowledge">Stato di riconoscimento</param>
        /// <returns>Ruolo aggiornato</returns>
        Task<FlowOrganizationUnitRoleDto> UpdateAcknowledgeAsync(Guid flowId, Guid organizationUnitId, bool acknowledge);

        /// <summary>
        /// Rimuove un ruolo per un'unità organizzativa da un flusso
        /// </summary>
        /// <param name="flowId">ID del flusso</param>
        /// <param name="organizationUnitId">ID dell'unità organizzativa</param>
        /// <returns>Esito dell'operazione</returns>
        Task<bool> RemoveAsync(Guid flowId, Guid organizationUnitId);
    }
} 