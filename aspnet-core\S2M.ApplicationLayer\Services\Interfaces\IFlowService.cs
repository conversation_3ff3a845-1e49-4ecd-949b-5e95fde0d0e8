using S2M.ApplicationLayer.Dto;

namespace S2M.ApplicationLayer.Services.Interfaces
{
    /// <summary>
    /// Interfaccia per il servizio dei flussi
    /// </summary>
    public interface IFlowService
    {
        /// <summary>
        /// Ottiene tutti i flussi
        /// </summary>
        /// <returns>Lista di flussi</returns>
        Task<IEnumerable<FlowDto>> GetAllAsync();

        /// <summary>
        /// Ottiene un flusso per ID
        /// </summary>
        /// <param name="id">ID del flusso</param>
        /// <returns>Flusso</returns>
        Task<FlowDto> GetByIdAsync(Guid id);

        /// <summary>
        /// Ottiene i flussi per tipo
        /// </summary>
        /// <param name="flowTypeId">ID del tipo di flusso</param>
        /// <returns>Lista di flussi</returns>
        Task<IEnumerable<FlowDto>> GetByFlowTypeIdAsync(Guid flowTypeId);

        /// <summary>
        /// Ottiene i flussi per unità organizzativa
        /// </summary>
        /// <param name="organizationUnitId">ID dell'unità organizzativa</param>
        /// <returns>Lista di flussi</returns>
        Task<IEnumerable<FlowDto>> GetByOrganizationUnitIdAsync(Guid organizationUnitId);

        /// <summary>
        /// Crea un nuovo flusso
        /// </summary>
        /// <param name="flowDto">Dati del flusso</param>
        /// <returns>Flusso creato</returns>
        Task<FlowDto> CreateAsync(FlowCreateDto flowDto);

        /// <summary>
        /// Aggiorna un flusso esistente
        /// </summary>
        /// <param name="id">ID del flusso</param>
        /// <param name="flowDto">Dati aggiornati del flusso</param>
        /// <returns>Esito dell'operazione</returns>
        Task<bool> UpdateAsync(Guid id, FlowDto flowDto);

        /// <summary>
        /// Elimina un flusso
        /// </summary>
        /// <param name="id">ID del flusso</param>
        /// <returns>Esito dell'operazione</returns>
        Task<bool> DeleteAsync(Guid id);

        /// <summary>
        /// Cambia lo stato di un flusso eseguendo un'azione
        /// </summary>
        /// <param name="id">ID del flusso</param>
        /// <param name="action">Nome dell'azione da eseguire</param>
        /// <returns>True se l'operazione è riuscita, False altrimenti</returns>
        Task<bool> ChangeStatusAsync(Guid id, string action);

        /// <summary>
        /// Manda il flusso in pre-approvazione
        /// </summary>
        /// <param name="flowId">ID del flusso</param>
        /// <returns>Flow aggiornato</returns>
        Task<FlowDto> SendForPreApprovalAsync(Guid flowId);
    }
} 