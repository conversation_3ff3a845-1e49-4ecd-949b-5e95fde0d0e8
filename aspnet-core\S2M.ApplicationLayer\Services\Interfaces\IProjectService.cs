using Microsoft.AspNetCore.Mvc;
using S2M.ApplicationLayer.Dto;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace S2M.ApplicationLayer.Services.Interfaces
{
    /// <summary>
    /// Interfaccia per il servizio dei progetti
    /// </summary>
    public interface IProjectService
    {
        /// <summary>
        /// Ottiene tutti i progetti
        /// </summary>
        /// <returns>Lista di progetti</returns>
        Task<IEnumerable<ProjectDto>> GetAllAsync();

        /// <summary>
        /// Ottiene un progetto per ID
        /// </summary>
        /// <param name="id">ID del progetto</param>
        /// <returns>Progetto</returns>
        Task<ProjectDto> GetByIdAsync(Guid id);

        /// <summary>
        /// Ottiene i progetti per area
        /// </summary>
        /// <param name="areaId">ID dell'area</param>
        /// <returns>Lista di progetti</returns>
        Task<IEnumerable<ProjectDto>> GetByAreaIdAsync(Guid areaId);

        /// <summary>
        /// Crea un nuovo progetto
        /// </summary>
        /// <param name="projectCreateDto">Dati per la creazione del progetto</param>
        /// <returns>Progetto creato</returns>
        Task<ProjectDto> CreateAsync(ProjectCreateDto projectCreateDto);

        /// <summary>
        /// Aggiorna un progetto esistente
        /// </summary>
        /// <param name="id">ID del progetto</param>
        /// <param name="projectDto">Dati aggiornati del progetto</param>
        /// <returns>Esito dell'operazione</returns>
        Task<bool> UpdateAsync(Guid id, ProjectCreateDto projectDto);

        /// <summary>
        /// Elimina un progetto
        /// </summary>
        /// <param name="id">ID del progetto</param>
        /// <returns>Esito dell'operazione</returns>
        Task<bool> DeleteAsync(Guid id);

        /// <summary>
        /// Aggiunge una milestone a un progetto
        /// </summary>
        /// <param name="projectId">ID del progetto</param>
        /// <param name="milestoneCreateDto">Dati per la creazione della milestone</param>
        /// <returns>Milestone aggiunta</returns>
        Task<ProjectMilestoneDto> AddMilestoneAsync(Guid projectId, ProjectMilestoneCreateDto milestoneCreateDto);

        /// <summary>
        /// Imposta lo stato di completamento di una milestone
        /// </summary>
        /// <param name="dto">DTO con ID della milestone e stato di completamento</param>
        /// <returns>Esito dell'operazione</returns>
        Task<bool> SetMilestoneCheckedAsync(Guid projectId, Guid milestoneId, ProjectMilestoneSetCheckedDto dto);

        /// <summary>
        /// Riordina le milestone di un progetto
        /// </summary>
        /// <param name="projectId">ID del progetto</param>
        /// <param name="dto">DTO con l'ordine delle milestone</param>
        /// <returns>Esito dell'operazione</returns>
        Task<bool> ReorderMilestonesAsync(Guid projectId, ProjectMilestoneReorderDto dto);

        Task<ProjectMilestoneDto> UpdateMilestoneAsync(Guid projectId, Guid milestoneId, ProjectMilestoneCreateDto milestoneUpdateDto);

        Task<bool> DeleteMilestoneAsync(Guid projectId, Guid milestoneId);

        /// <summary>
        /// Ottiene gli utenti delle unità organizzative del progetto (owner e collaboratori)
        /// </summary>
        /// <param name="projectId">ID del progetto</param>
        /// <returns>Lista di utenti</returns>
        Task<IEnumerable<UserDto>> GetProjectUsersAsync(Guid projectId);
    }
} 