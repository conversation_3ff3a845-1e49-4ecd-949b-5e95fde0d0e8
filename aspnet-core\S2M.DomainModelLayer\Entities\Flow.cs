using S2M.DomainModelLayer.Enums;
using S2M.DomainModelLayer.Services;

namespace S2M.DomainModelLayer.Entities
{
    public class Flow : FullAuditedEntity
    {
        private DocumentFlowStateMachine _stateMachine;

        public string Subject { get; set; }
        public Guid FlowTypeId { get; set; }
        public FlowStatus FlowStatus { get; set; }
        public Guid? DocumentFileId { get; set; }
        public string JoshProtocol { get; set; }
        public Guid OrganizationUnitId { get; set; }
        public DateTime? TakingChargeDate { get; set; }
        public Guid? ProjectId { get; set; }

        public virtual FlowType FlowType { get; set; }
        public virtual DocumentFile DocumentFile { get; set; }
        public virtual OrganizationUnit OrganizationUnit { get; set; }
        public virtual Project Project { get; set; }
        public virtual ICollection<FlowUserRole> UserRoles { get; set; }
        public virtual ICollection<FlowComment> Comments { get; set; }
        public virtual ICollection<FlowAttachment> Attachments { get; set; }
        public virtual ICollection<Sheet> Sheets { get; set; }
        public virtual ICollection<Entities.Attribute> Attributes { get; set; }

        // Costruttore vuoto richiesto da EF
        public Flow()
        {
            InitializeStateMachine();
        }

        // Costruttore esplicito, ad esempio per creazione manuale
        public Flow(FlowStatus initialState)
        {
            FlowStatus = initialState;
            InitializeStateMachine();
        }

        private void InitializeStateMachine()
        {
            _stateMachine = new DocumentFlowStateMachine(() => FlowStatus, s => FlowStatus = s);
        }

        // Metodi di dominio per agire sulla FSM
        public bool TryApplyAction(Constants.Action action)
        {
            return _stateMachine.TryFireTrigger(action);
        }

        public bool CanApplyAction(Constants.Action action)
        {
            return _stateMachine.CanFire(action);
        }

        public IEnumerable<Constants.Action> GetAvailableActions()
        {
            return _stateMachine.GetPermittedTriggers();
        }
    }
}