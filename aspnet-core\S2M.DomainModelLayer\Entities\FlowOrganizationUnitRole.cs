using System;
using S2M.DomainModelLayer.Enums;

namespace S2M.DomainModelLayer.Entities
{
    public class FlowOrganizationUnitRole : FullAuditedEntity
    {
        public Guid FlowId { get; set; }
        public Guid OrganizationUnitId { get; set; }
        public FlowOrganizationUnitRoleType Role { get; set; }
        public bool? Acknowledge { get; set; }
        public DateTime? AcknowledgeDate { get; set; }
        
        public virtual Flow Flow { get; set; }
        public virtual OrganizationUnit OrganizationUnit { get; set; }
    }
} 