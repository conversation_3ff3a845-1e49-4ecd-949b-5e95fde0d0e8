using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using S2M.DomainModelLayer.Entities;

namespace S2M.InfrastructureLayer.Data.EntitiesConfig
{
    public class FlowConfiguration : FullAuditedEntityConfiguration<Flow>
    {
        public override void Configure(EntityTypeBuilder<Flow> builder)
        {
            base.Configure(builder);

            builder.ToTable("Flow", "public");

            builder.Property(e => e.Subject)
                .IsRequired()
                .HasMaxLength(512);

            builder.Property(e => e.FlowStatus)
                .IsRequired();

            builder.Property(e => e.JoshProtocol)
                .HasMaxLength(100);

            builder.Property(e => e.OrganizationUnitId)
                .IsRequired();

            builder.Property(e => e.TakingChargeDate);

            builder.HasOne(e => e.FlowType)
                .WithMany(e => e.Flows)
                .HasForeignKey(e => e.FlowTypeId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(e => e.DocumentFile)
                .WithMany()
                .HasForeignKey(e => e.DocumentFileId)
                .OnDelete(DeleteBehavior.Restrict)
                .IsRequired(false);

            builder.HasOne(e => e.Project)
                .WithMany()
                .HasForeignKey(e => e.ProjectId)
                .OnDelete(DeleteBehavior.Restrict)
                .IsRequired(false);

            builder.HasOne(e => e.OrganizationUnit)
                .WithMany()
                .HasForeignKey(e => e.OrganizationUnitId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasMany(e => e.UserRoles)
                .WithOne(e => e.Flow)
                .HasForeignKey(e => e.FlowId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(e => e.Comments)
                .WithOne(e => e.Flow)
                .HasForeignKey(e => e.FlowId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(e => e.Attachments)
                .WithOne(e => e.Flow)
                .HasForeignKey(e => e.FlowId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(e => e.Sheets)
                .WithOne(e => e.Flow)
                .HasForeignKey(e => e.FlowId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(e => e.Attributes)
                .WithOne(e => e.Flow)
                .HasForeignKey(e => e.FlowId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
} 