using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using S2M.DomainModelLayer.Entities;

namespace S2M.InfrastructureLayer.Data.EntitiesConfig
{
    public class FlowOrganizationUnitRoleEntityTypeConfiguration : IEntityTypeConfiguration<FlowOrganizationUnitRole>
    {
        public void Configure(EntityTypeBuilder<FlowOrganizationUnitRole> builder)
        {
            builder.ToTable("FlowOrganizationUnitRole", "public");

            builder.HasKey(x => x.Id);

            builder.Property(x => x.FlowId)
                .IsRequired();

            builder.Property(x => x.OrganizationUnitId)
                .IsRequired();

            builder.Property(x => x.Role)
                .IsRequired();

            // Relazione con Flow
            builder.HasOne(x => x.Flow)
                .WithMany()
                .HasForeignKey(x => x.FlowId)
                .OnDelete(DeleteBehavior.Cascade);

            // Relazione con OrganizationUnit
            builder.HasOne(x => x.OrganizationUnit)
                .WithMany()
                .HasForeignKey(x => x.OrganizationUnitId)
                .OnDelete(DeleteBehavior.Restrict);

            // Indice composto per evitare duplicati
            builder.HasIndex(x => new { x.FlowId, x.OrganizationUnitId, x.Role })
                .IsUnique();
        }
    }
} 