using Microsoft.EntityFrameworkCore;
using S2M.DomainModelLayer.Entities;
using S2M.InfrastructureLayer.Data.EntitiesConfig;
using S2M.InfrastructureLayer.Extensions;

namespace S2M.InfrastructureLayer.Data
{
    public class S2MDbContext : DbContext
    {
        public S2MDbContext(DbContextOptions<S2MDbContext> options) : base(options)
        {
        }

        public DbSet<Area> Area { get; set; }
        public DbSet<Project> Project { get; set; }
        public DbSet<ProjectMilestone> ProjectMilestone { get; set; }
        public DbSet<BinaryObject> BinaryObject { get; set; }
        public DbSet<DocumentFile> DocumentFile { get; set; }
        public DbSet<FlowType> FlowType { get; set; }
        public DbSet<FlowTypeSheet> FlowTypeSheet { get; set; }
        public DbSet<FlowTypeAttribute> FlowTypeAttribute { get; set; }
        public DbSet<Flow> Flow { get; set; }
        public DbSet<FlowUserRole> FlowUserRole { get; set; }
        public DbSet<FlowOrganizationUnitRole> FlowOrganizationUnitRole { get; set; }
        public DbSet<FlowComment> FlowComment { get; set; }
        public DbSet<FlowAttachment> FlowAttachment { get; set; }
        public DbSet<Sheet> Sheet { get; set; }
        public DbSet<SheetHistory> SheetHistory { get; set; }
        public DbSet<SheetComment> SheetComment { get; set; }
        public DbSet<DomainModelLayer.Entities.Attribute> FlowAttribute { get; set; }
        public DbSet<EnumType> EnumType { get; set; }
        public DbSet<EnumTypeValue> EnumTypeValue { get; set; }
        public DbSet<OrganizationUnit> OrganizationUnit { get; set; }
        public DbSet<CommandRole> CommandRole { get; set; }
        public DbSet<OrganizationUnitCommandAssignment> OrganizationUnitCommandAssignment { get; set; }
        public DbSet<UnitType> UnitType { get; set; }
        public DbSet<ProjectOrganizationUnitRole> ProjectOrganizationUnitRole { get; set; }

        // Entità per l'autenticazione e l'autorizzazione
        public DbSet<User> User { get; set; }
        public DbSet<Role> Role { get; set; }
        public DbSet<Permission> Permission { get; set; }
        public DbSet<UserRole> UserRole { get; set; }
        public DbSet<RolePermission> RolePermission { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Apply all entity configurations
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(S2MDbContext).Assembly);

            // I dati di seed sono stati spostati nelle classi seeder dedicate
            // e vengono applicati all'avvio dell'applicazione
        }
    }
}