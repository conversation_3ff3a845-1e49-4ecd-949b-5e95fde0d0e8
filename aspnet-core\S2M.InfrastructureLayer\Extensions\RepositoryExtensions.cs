using Microsoft.Extensions.DependencyInjection;
using S2M.InfrastructureLayer.Repository;

namespace S2M.InfrastructureLayer.Extensions
{
    /// <summary>
    /// Estensioni per la configurazione dei repository
    /// </summary>
    public static class RepositoryExtensions
    {
        /// <summary>
        /// Aggiunge i repository al container di dependency injection
        /// </summary>
        /// <param name="services">Collection di servizi</param>
        /// <returns>Collection di servizi aggiornata</returns>
        public static IServiceCollection AddRepositories(this IServiceCollection services)
        {
            // Repository generico
            services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

            // Registra i repository
            services.AddScoped<IAreaRepository, AreaRepository>();
            services.AddScoped<IProjectRepository, ProjectRepository>();
            services.AddScoped<IFlowTypeRepository, FlowTypeRepository>();
            services.AddScoped<IFlowRepository, FlowRepository>();
            services.AddScoped<IFlowUserRepository, FlowUserRepository>();
            services.AddScoped<IBinaryObjectRepository, BinaryObjectRepository>();
            services.AddScoped<IDocumentFileRepository, DocumentFileRepository>();
            services.AddScoped<IEnumTypeRepository, EnumTypeRepository>();
            services.AddScoped<IAttributeRepository, AttributeRepository>();
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IRoleRepository, RoleRepository>();
            services.AddScoped<ICommandRoleRepository, CommandRoleRepository>();
            services.AddScoped<IUnitTypeRepository, UnitTypeRepository>();
            services.AddScoped<IOrganizationUnitCommandAssignmentRepository, OrganizationUnitCommandAssignmentRepository>();
            services.AddScoped<IOrganizationUnitRepository, OrganizationUnitRepository>();
            services.AddScoped<ISheetCommentRepository, SheetCommentRepository>();
            services.AddScoped<ISheetRepository, SheetRepository>();
            services.AddScoped<IProjectOrganizationUnitRoleRepository, ProjectOrganizationUnitRoleRepository>();
            services.AddScoped<IFlowOrganizationUnitRoleRepository, FlowOrganizationUnitRoleRepository>();

            return services;
        }
    }
}