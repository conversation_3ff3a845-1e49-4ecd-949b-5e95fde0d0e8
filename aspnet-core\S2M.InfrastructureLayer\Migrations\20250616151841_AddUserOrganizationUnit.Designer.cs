﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using S2M.InfrastructureLayer.Data;

#nullable disable

namespace S2M.InfrastructureLayer.Migrations
{
    [DbContext(typeof(S2MDbContext))]
    [Migration("20250616151841_AddUserOrganizationUnit")]
    partial class AddUserOrganizationUnit
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Area", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.ToTable("Area", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Attribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("Id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("DeletionTime");

                    b.Property<Guid>("FlowId")
                        .HasColumnType("uuid")
                        .HasColumnName("FlowId");

                    b.Property<Guid>("FlowTypeAttributeId")
                        .HasColumnType("uuid")
                        .HasColumnName("FlowTypeAttributeId");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean")
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)")
                        .HasColumnName("Value");

                    b.HasKey("Id");

                    b.HasIndex("FlowId");

                    b.HasIndex("FlowTypeAttributeId");

                    b.ToTable("FlowAttribute", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.BinaryObject", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<byte[]>("Content")
                        .IsRequired()
                        .HasColumnType("bytea");

                    b.Property<long>("ContentSize")
                        .HasColumnType("bigint");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("BinaryObject", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.CommandRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<Guid>("UnitTypeId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UnitTypeId");

                    b.ToTable("CommandRole");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.DocumentFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("BinaryObjectId")
                        .HasColumnType("uuid");

                    b.Property<long>("ContentSize")
                        .HasColumnType("bigint");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("BinaryObjectId");

                    b.ToTable("DocumentFile", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.EnumType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.ToTable("EnumType");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.EnumTypeValue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("EnumTypeId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Value")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("EnumTypeId");

                    b.ToTable("EnumTypeValue");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Flow", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DepartmentCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DepartmentName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("DocumentFileId")
                        .HasColumnType("uuid");

                    b.Property<int>("FlowStatus")
                        .HasColumnType("integer");

                    b.Property<Guid>("FlowTypeId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("JoshProtocol")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<DateTime?>("TakingChargeDate")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("DocumentFileId");

                    b.HasIndex("FlowTypeId");

                    b.HasIndex("ProjectId");

                    b.ToTable("Flow", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowAttachment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("BinaryObjectId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid>("FlowId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("BinaryObjectId");

                    b.HasIndex("FlowId");

                    b.ToTable("FlowAttachment", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowComment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasMaxLength(2048)
                        .HasColumnType("character varying(2048)");

                    b.Property<int>("CommentType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FlowId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("FlowId");

                    b.ToTable("FlowComment", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("Instructions")
                        .IsRequired()
                        .HasMaxLength(2048)
                        .HasColumnType("character varying(2048)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("OfficeExclusive")
                        .HasColumnType("boolean");

                    b.Property<string>("Template")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("FlowType", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowTypeAttribute", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AttributeType")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("EnumTypeId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("FlowTypeId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<string>("TemplatePlaceholder")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("EnumTypeId");

                    b.HasIndex("FlowTypeId");

                    b.ToTable("FlowTypeAttribute", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowTypeSheet", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<Guid>("FlowTypeId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<string>("TemplatePlaceholder")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("FlowTypeId");

                    b.ToTable("FlowTypeSheet", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowUserRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool?>("Acknowledge")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("AcknowledgeDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FlowId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<int>("Role")
                        .HasColumnType("integer");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("FlowId");

                    b.ToTable("FlowUserRole", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.OrganizationUnit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ParentUnitId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UnitTypeId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ParentUnitId");

                    b.HasIndex("UnitTypeId");

                    b.ToTable("OrganizationUnit", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.OrganizationUnitCommandAssignment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CommandRoleId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("OrganizationUnitId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CommandRoleId");

                    b.HasIndex("OrganizationUnitId");

                    b.HasIndex("UserId");

                    b.ToTable("OrganizationUnitCommandAssignment");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Permission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Permission", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Project", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("AreaId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EndState")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Subtitle")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("Summary")
                        .IsRequired()
                        .HasMaxLength(2048)
                        .HasColumnType("character varying(2048)");

                    b.HasKey("Id");

                    b.HasIndex("AreaId");

                    b.ToTable("Project", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.ProjectMilestone", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("Checked")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("Order")
                        .HasColumnType("integer");

                    b.Property<Guid>("ProjectId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectMilestone", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.ProjectOrganizationUnitRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("OrganizationUnitId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProjectId")
                        .HasColumnType("uuid");

                    b.Property<int>("UnitRole")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationUnitId");

                    b.HasIndex("ProjectId");

                    b.ToTable("ProjectOrganizationUnitRole", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsDefault")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Role", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.RolePermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("PermissionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("PermissionId");

                    b.HasIndex("RoleId", "PermissionId")
                        .IsUnique();

                    b.ToTable("RolePermission", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Sheet", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Content")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FlowId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("FlowTypeSheetId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("FlowId");

                    b.HasIndex("FlowTypeSheetId");

                    b.ToTable("Sheet", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.SheetComment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Comment")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("FlowStatus")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ParentCommentId")
                        .HasColumnType("uuid");

                    b.Property<string>("RefId")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid>("SheetId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ParentCommentId")
                        .HasDatabaseName("IX_SheetComment_ParentCommentId");

                    b.HasIndex("SheetId", "RefId")
                        .HasDatabaseName("IX_SheetComment_SheetId_RefId");

                    b.ToTable("SheetComment", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.SheetHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("SheetId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("SheetId");

                    b.ToTable("SheetHistory", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.UnitType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("UnitType", (string)null);
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Rank")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("Username", "IsDeleted")
                        .IsUnique()
                        .HasFilter("\"IsDeleted\" = false");

                    b.ToTable("User", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.UserOrganizationUnit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("OrganizationUnitId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationUnitId");

                    b.HasIndex("UserId");

                    b.ToTable("UserOrganizationUnit", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.UserRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId", "RoleId")
                        .IsUnique();

                    b.ToTable("UserRole", "public");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Attribute", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.Flow", "Flow")
                        .WithMany("Attributes")
                        .HasForeignKey("FlowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("S2M.DomainModelLayer.Entities.FlowTypeAttribute", "FlowTypeAttribute")
                        .WithMany("Attributes")
                        .HasForeignKey("FlowTypeAttributeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Flow");

                    b.Navigation("FlowTypeAttribute");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.CommandRole", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.UnitType", "UnitType")
                        .WithMany("CommandRoles")
                        .HasForeignKey("UnitTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("UnitType");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.DocumentFile", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.BinaryObject", "BinaryObject")
                        .WithMany()
                        .HasForeignKey("BinaryObjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("BinaryObject");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.EnumTypeValue", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.EnumType", "EnumType")
                        .WithMany("Values")
                        .HasForeignKey("EnumTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EnumType");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Flow", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.DocumentFile", "DocumentFile")
                        .WithMany()
                        .HasForeignKey("DocumentFileId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("S2M.DomainModelLayer.Entities.FlowType", "FlowType")
                        .WithMany("Flows")
                        .HasForeignKey("FlowTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("S2M.DomainModelLayer.Entities.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("DocumentFile");

                    b.Navigation("FlowType");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowAttachment", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.BinaryObject", "BinaryObject")
                        .WithMany()
                        .HasForeignKey("BinaryObjectId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("S2M.DomainModelLayer.Entities.Flow", "Flow")
                        .WithMany("Attachments")
                        .HasForeignKey("FlowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BinaryObject");

                    b.Navigation("Flow");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowComment", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.Flow", "Flow")
                        .WithMany("Comments")
                        .HasForeignKey("FlowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Flow");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowTypeAttribute", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.EnumType", "EnumType")
                        .WithMany("FlowTypeAttributes")
                        .HasForeignKey("EnumTypeId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("S2M.DomainModelLayer.Entities.FlowType", "FlowType")
                        .WithMany("Attributes")
                        .HasForeignKey("FlowTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("EnumType");

                    b.Navigation("FlowType");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowTypeSheet", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.FlowType", "FlowType")
                        .WithMany("Sheets")
                        .HasForeignKey("FlowTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("FlowType");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowUserRole", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.Flow", "Flow")
                        .WithMany("UserRoles")
                        .HasForeignKey("FlowId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Flow");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.OrganizationUnit", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.OrganizationUnit", "ParentUnit")
                        .WithMany("ChildUnits")
                        .HasForeignKey("ParentUnitId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("S2M.DomainModelLayer.Entities.UnitType", "UnitType")
                        .WithMany("OrganizationUnits")
                        .HasForeignKey("UnitTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ParentUnit");

                    b.Navigation("UnitType");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.OrganizationUnitCommandAssignment", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.CommandRole", "CommandRoles")
                        .WithMany("CommandAssignment")
                        .HasForeignKey("CommandRoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("S2M.DomainModelLayer.Entities.OrganizationUnit", "OrganizationUnit")
                        .WithMany("CommandAssignments")
                        .HasForeignKey("OrganizationUnitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("S2M.DomainModelLayer.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CommandRoles");

                    b.Navigation("OrganizationUnit");

                    b.Navigation("User");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Project", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.Area", "Area")
                        .WithMany()
                        .HasForeignKey("AreaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Area");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.ProjectMilestone", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.Project", "Project")
                        .WithMany("Milestones")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.ProjectOrganizationUnitRole", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.OrganizationUnit", "OrganizationUnit")
                        .WithMany("ProjectRoles")
                        .HasForeignKey("OrganizationUnitId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("S2M.DomainModelLayer.Entities.Project", "Project")
                        .WithMany("OrganizationUnits")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("OrganizationUnit");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.RolePermission", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.Permission", "Permission")
                        .WithMany("RolePermissions")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("S2M.DomainModelLayer.Entities.Role", "Role")
                        .WithMany("RolePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Permission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Sheet", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.Flow", "Flow")
                        .WithMany("Sheets")
                        .HasForeignKey("FlowId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("S2M.DomainModelLayer.Entities.FlowTypeSheet", "FlowTypeSheet")
                        .WithMany("Sheets")
                        .HasForeignKey("FlowTypeSheetId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Flow");

                    b.Navigation("FlowTypeSheet");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.SheetComment", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.SheetComment", "ParentComment")
                        .WithMany("Replies")
                        .HasForeignKey("ParentCommentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("S2M.DomainModelLayer.Entities.Sheet", "Sheet")
                        .WithMany("Comments")
                        .HasForeignKey("SheetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ParentComment");

                    b.Navigation("Sheet");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.SheetHistory", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.Sheet", "Sheet")
                        .WithMany("History")
                        .HasForeignKey("SheetId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Sheet");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.UserOrganizationUnit", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.OrganizationUnit", "OrganizationUnit")
                        .WithMany("UserOrganizationUnits")
                        .HasForeignKey("OrganizationUnitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("S2M.DomainModelLayer.Entities.User", "User")
                        .WithMany("UserOrganizationUnits")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("OrganizationUnit");

                    b.Navigation("User");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.UserRole", b =>
                {
                    b.HasOne("S2M.DomainModelLayer.Entities.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("S2M.DomainModelLayer.Entities.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.CommandRole", b =>
                {
                    b.Navigation("CommandAssignment");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.EnumType", b =>
                {
                    b.Navigation("FlowTypeAttributes");

                    b.Navigation("Values");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Flow", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Attributes");

                    b.Navigation("Comments");

                    b.Navigation("Sheets");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowType", b =>
                {
                    b.Navigation("Attributes");

                    b.Navigation("Flows");

                    b.Navigation("Sheets");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowTypeAttribute", b =>
                {
                    b.Navigation("Attributes");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.FlowTypeSheet", b =>
                {
                    b.Navigation("Sheets");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.OrganizationUnit", b =>
                {
                    b.Navigation("ChildUnits");

                    b.Navigation("CommandAssignments");

                    b.Navigation("ProjectRoles");

                    b.Navigation("UserOrganizationUnits");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Permission", b =>
                {
                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Project", b =>
                {
                    b.Navigation("Milestones");

                    b.Navigation("OrganizationUnits");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Role", b =>
                {
                    b.Navigation("RolePermissions");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.Sheet", b =>
                {
                    b.Navigation("Comments");

                    b.Navigation("History");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.SheetComment", b =>
                {
                    b.Navigation("Replies");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.UnitType", b =>
                {
                    b.Navigation("CommandRoles");

                    b.Navigation("OrganizationUnits");
                });

            modelBuilder.Entity("S2M.DomainModelLayer.Entities.User", b =>
                {
                    b.Navigation("UserOrganizationUnits");

                    b.Navigation("UserRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
