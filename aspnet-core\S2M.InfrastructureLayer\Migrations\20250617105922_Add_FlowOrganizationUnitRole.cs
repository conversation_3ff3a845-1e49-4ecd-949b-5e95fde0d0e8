﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace S2M.InfrastructureLayer.Migrations
{
    /// <inheritdoc />
    public partial class Add_FlowOrganizationUnitRole : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "FlowOrganizationUnitRole",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FlowId = table.Column<Guid>(type: "uuid", nullable: false),
                    OrganizationUnitId = table.Column<Guid>(type: "uuid", nullable: false),
                    Role = table.Column<int>(type: "integer", nullable: false),
                    Acknowledge = table.Column<bool>(type: "boolean", nullable: true),
                    AcknowledgeDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeleterId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FlowOrganizationUnitRole", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FlowOrganizationUnitRole_Flow_FlowId",
                        column: x => x.FlowId,
                        principalSchema: "public",
                        principalTable: "Flow",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_FlowOrganizationUnitRole_OrganizationUnit_OrganizationUnitId",
                        column: x => x.OrganizationUnitId,
                        principalSchema: "public",
                        principalTable: "OrganizationUnit",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_FlowOrganizationUnitRole_FlowId_OrganizationUnitId_Role",
                schema: "public",
                table: "FlowOrganizationUnitRole",
                columns: new[] { "FlowId", "OrganizationUnitId", "Role" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FlowOrganizationUnitRole_OrganizationUnitId",
                schema: "public",
                table: "FlowOrganizationUnitRole",
                column: "OrganizationUnitId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "FlowOrganizationUnitRole",
                schema: "public");
        }
    }
}
